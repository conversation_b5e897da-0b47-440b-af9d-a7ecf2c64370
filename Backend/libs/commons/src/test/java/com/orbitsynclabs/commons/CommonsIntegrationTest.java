package com.orbitsynclabs.commons;

import com.orbitsynclabs.commons.exception.BusinessException;
import com.orbitsynclabs.commons.exception.ErrorCode;
import com.orbitsynclabs.commons.exception.GlobalExceptionHandler;
import com.orbitsynclabs.commons.logging.LogContext;
import com.orbitsynclabs.commons.logging.StructuredLogger;
import com.orbitsynclabs.commons.response.BaseResponse;
import com.orbitsynclabs.commons.response.ErrorResponse;
import com.orbitsynclabs.commons.response.ResponseUtils;
import com.orbitsynclabs.commons.response.ValidationError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Integration test to verify all components work together
 */
@ExtendWith(MockitoExtension.class)
class CommonsIntegrationTest {

    @Mock
    private StructuredLogger structuredLogger;

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Mock
    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        request.addHeader("X-Request-ID", "integration-test-request-id");
    }

    @Test
    void testExceptionHandlingWithLoggingIntegration() {
        // Setup
        BusinessException ex = new BusinessException(ErrorCode.BUS_RESOURCE_NOT_FOUND, "Resource not found");
        
        // Test exception handling
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleBusinessException(ex, request);
        
        // Verify response
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Business Error", errorResponse.getError());
        assertEquals("Resource not found", errorResponse.getMessage());
        assertEquals(ErrorCode.BUS_RESOURCE_NOT_FOUND, errorResponse.getErrorCode());
        assertEquals("integration-test-request-id", errorResponse.getRequestId());
        
        // Verify structured logger was called
        verify(structuredLogger).getLogger(GlobalExceptionHandler.class);
    }

    @Test
    void testResponseFrameworkWithLoggingIntegration() {
        // Setup test data
        String testData = "integration test data";
        Map<String, Object> metadata = Map.of("test-key", "test-value");
        
        // Create response using ResponseUtils
        ResponseEntity<BaseResponse<String>> response = ResponseUtils.success(testData, "Success message", metadata);
        
        // Verify response
        assertEquals(HttpStatus.OK, response.getStatusCode());
        BaseResponse<String> baseResponse = response.getBody();
        assertNotNull(baseResponse);
        assertEquals(testData, baseResponse.getData());
        assertEquals("Success message", baseResponse.getMessage());
        assertEquals(200, baseResponse.getStatusCode());
        assertEquals(metadata, baseResponse.getMetadata());
        assertNotNull(baseResponse.getRequestId());
        assertNotNull(baseResponse.getTimestamp());
        
        // Verify it's successful
        assertTrue(baseResponse.isSuccess());
    }

    @Test
    void testErrorResponseWithValidationErrorsIntegration() {
        // Setup validation errors
        List<ValidationError> validationErrors = List.of(
            ValidationError.of("email", "Invalid email format"),
            ValidationError.of("password", "Password must be at least 8 characters")
        );
        
        // Create error response
        ResponseEntity<ErrorResponse> response = ResponseUtils.badRequest(
            "Validation failed", "VAL_001", validationErrors
        );
        
        // Verify response
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Bad Request", errorResponse.getError());
        assertEquals("Validation failed", errorResponse.getMessage());
        assertEquals("VAL_001", errorResponse.getErrorCode());
        assertTrue(errorResponse.hasValidationErrors());
        assertEquals(2, errorResponse.getValidationErrors().size());
        
        // Verify validation error details
        ValidationError firstError = errorResponse.getValidationErrors().get(0);
        assertEquals("email", firstError.getField());
        assertEquals("Invalid email format", firstError.getMessage());
    }

    @Test
    void testLogContextWithResponseIntegration() {
        // Setup log context
        LogContext logContext = new LogContext();
        logContext.createContext("user123", "request456", "commons-service", "test");
        
        // Verify context values
        assertEquals("user123", logContext.getUserId());
        assertEquals("request456", logContext.getRequestId());
        assertEquals("commons-service", logContext.getServiceName());
        assertEquals("test", logContext.getEnvironment());
        assertNotNull(logContext.getCorrelationId());
        
        // Create response with context
        BaseResponse<String> response = BaseResponse.success("test data");
        
        // Verify both work independently
        assertNotNull(response.getRequestId());
        assertNotNull(logContext.getCorrelationId());
        assertNotEquals(response.getRequestId(), logContext.getCorrelationId());
    }

    @Test
    void testCompleteWorkflowIntegration() {
        // 1. Start logging context
        LogContext logContext = new LogContext();
        logContext.createContext("user123", "request456", "commons-service", "test");
        
        // 2. Log business operation
        Map<String, Object> operationMetadata = Map.of(
            "operation", "create-user",
            "status", "started"
        );
        structuredLogger.logBusinessOperation(
            structuredLogger.getLogger(getClass()), 
            "create-user", 
            "started", 
            operationMetadata
        );
        
        // 3. Simulate business logic that might fail
        try {
            // Simulate validation failure
            throw new BusinessException(ErrorCode.BUS_INVALID_INPUT, "Invalid user data");
        } catch (BusinessException ex) {
            // 4. Handle exception
            ResponseEntity<ErrorResponse> errorResponse = globalExceptionHandler.handleBusinessException(ex, request);
            
            // 5. Log the error
            structuredLogger.logError(
                structuredLogger.getLogger(getClass()),
                "BusinessException",
                ex.getMessage(),
                Map.of("errorCode", ex.getErrorCode()),
                ex
            );
            
            // 6. Verify complete workflow
            assertEquals(HttpStatus.BAD_REQUEST, errorResponse.getStatusCode());
            ErrorResponse error = errorResponse.getBody();
            assertEquals("Business Error", error.getError());
            assertEquals(ErrorCode.BUS_INVALID_INPUT, error.getErrorCode());
            
            // 7. Verify context is still available
            assertNotNull(logContext.getCorrelationId());
            assertEquals("user123", logContext.getUserId());
        }
        
        // 8. Clear context
        logContext.clearContext();
        assertFalse(logContext.hasContext());
    }

    @Test
    void testResponseUtilsIntegration() {
        // Test all ResponseUtils methods
        ResponseEntity<BaseResponse<String>> successResponse = ResponseUtils.success("test data");
        assertEquals(HttpStatus.OK, successResponse.getStatusCode());
        
        ResponseEntity<BaseResponse<String>> createdResponse = ResponseUtils.created("new data", "Created successfully");
        assertEquals(HttpStatus.CREATED, createdResponse.getStatusCode());
        
        ResponseEntity<BaseResponse<String>> noContentResponse = ResponseUtils.noContent("Deleted successfully");
        assertEquals(HttpStatus.NO_CONTENT, noContentResponse.getStatusCode());
        
        ResponseEntity<ErrorResponse> badRequestResponse = ResponseUtils.badRequest("Bad request", "VAL_001");
        assertEquals(HttpStatus.BAD_REQUEST, badRequestResponse.getStatusCode());
        
        ResponseEntity<ErrorResponse> notFoundResponse = ResponseUtils.notFound("Not found", "BUS_001");
        assertEquals(HttpStatus.NOT_FOUND, notFoundResponse.getStatusCode());
        
        ResponseEntity<ErrorResponse> internalErrorResponse = ResponseUtils.internalServerError("Server error", "SYS_001");
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, internalErrorResponse.getStatusCode());
    }

    @Test
    void testErrorCodeIntegration() {
        // Test error codes are properly defined
        assertNotNull(ErrorCode.BUS_RESOURCE_NOT_FOUND);
        assertNotNull(ErrorCode.SYS_INTERNAL_ERROR);
        assertNotNull(ErrorCode.VAL_REQUIRED_FIELD);
        assertNotNull(ErrorCode.SEC_AUTHENTICATION_FAILED);
        
        // Test error code formats
        assertTrue(ErrorCode.BUS_RESOURCE_NOT_FOUND.startsWith("BUS_"));
        assertTrue(ErrorCode.SYS_INTERNAL_ERROR.startsWith("SYS_"));
        assertTrue(ErrorCode.VAL_REQUIRED_FIELD.startsWith("VAL_"));
        assertTrue(ErrorCode.SEC_AUTHENTICATION_FAILED.startsWith("SEC_"));
    }
}