package com.orbitsynclabs.commons.response;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Test class for BaseResponse
 */
class BaseResponseTest {

    @Test
    void testSuccessResponseWithData() {
        String testData = "test data";
        
        BaseResponse<String> response = BaseResponse.success(testData);
        
        assertEquals(testData, response.getData());
        assertEquals("Success", response.getMessage());
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getRequestId());
        assertNotNull(response.getTimestamp());
        assertNull(response.getMetadata());
        assertTrue(response.isSuccess());
        assertFalse(response.isClientError());
        assertFalse(response.isServerError());
    }

    @Test
    void testSuccessResponseWithDataAndMessage() {
        String testData = "test data";
        String message = "Custom success message";
        
        BaseResponse<String> response = BaseResponse.success(testData, message);
        
        assertEquals(testData, response.getData());
        assertEquals(message, response.getMessage());
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getRequestId());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testSuccessResponseWithDataMessageAndMetadata() {
        String testData = "test data";
        String message = "Custom success message";
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("key1", "value1");
        metadata.put("key2", 123);
        
        BaseResponse<String> response = BaseResponse.success(testData, message, metadata);
        
        assertEquals(testData, response.getData());
        assertEquals(message, response.getMessage());
        assertEquals(200, response.getStatusCode());
        assertEquals(metadata, response.getMetadata());
        assertNotNull(response.getRequestId());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testSuccessResponseWithoutData() {
        String message = "Operation completed successfully";
        
        BaseResponse<String> response = BaseResponse.success(message);
        
        assertNull(response.getData());
        assertEquals(message, response.getMessage());
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getRequestId());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testCreatedResponse() {
        String testData = "new resource";
        String message = "Resource created successfully";
        
        BaseResponse<String> response = BaseResponse.created(testData, message);
        
        assertEquals(testData, response.getData());
        assertEquals(message, response.getMessage());
        assertEquals(201, response.getStatusCode());
        assertTrue(response.isSuccess());
    }

    @Test
    void testAcceptedResponse() {
        String testData = "processing data";
        String message = "Request accepted for processing";
        
        BaseResponse<String> response = BaseResponse.accepted(testData, message);
        
        assertEquals(testData, response.getData());
        assertEquals(message, response.getMessage());
        assertEquals(202, response.getStatusCode());
        assertTrue(response.isSuccess());
    }

    @Test
    void testNoContentResponse() {
        String message = "Resource deleted successfully";
        
        BaseResponse<String> response = BaseResponse.noContent(message);
        
        assertNull(response.getData());
        assertEquals(message, response.getMessage());
        assertEquals(204, response.getStatusCode());
        assertTrue(response.isSuccess());
    }

    @Test
    void testWithMetadata() {
        BaseResponse<String> response = BaseResponse.success("test data");
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("key1", "value1");
        
        response = response.withMetadata(metadata);
        
        assertEquals(metadata, response.getMetadata());
    }

    @Test
    void testWithMetadataKeyValue() {
        BaseResponse<String> response = BaseResponse.success("test data");
        
        response = response.withMetadata("key", "value");
        
        assertNotNull(response.getMetadata());
        assertEquals("value", response.getMetadata().get("key"));
    }

    @Test
    void testBuilderPattern() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("key1", "value1");
        
        BaseResponse<String> response = BaseResponse.<String>builder()
                .data("test data")
                .message("Custom message")
                .statusCode(200)
                .requestId(UUID.randomUUID().toString())
                .timestamp(LocalDateTime.now())
                .metadata(metadata)
                .build();
        
        assertEquals("test data", response.getData());
        assertEquals("Custom message", response.getMessage());
        assertEquals(200, response.getStatusCode());
        assertEquals(metadata, response.getMetadata());
    }

    @Test
    void testRequestIdsAreUnique() {
        BaseResponse<String> response1 = BaseResponse.success("data1");
        BaseResponse<String> response2 = BaseResponse.success("data2");
        
        assertNotEquals(response1.getRequestId(), response2.getRequestId());
    }

    @Test
    void testTimestampsAreDifferent() {
        BaseResponse<String> response1 = BaseResponse.success("data1");
        BaseResponse<String> response2 = BaseResponse.success("data2");
        
        assertNotEquals(response1.getTimestamp(), response2.getTimestamp());
    }

    @Test
    void testClientErrorDetection() {
        BaseResponse<String> response = BaseResponse.<String>builder()
                .statusCode(400)
                .build();
        
        assertFalse(response.isSuccess());
        assertTrue(response.isClientError());
        assertFalse(response.isServerError());
    }

    @Test
    void testServerErrorDetection() {
        BaseResponse<String> response = BaseResponse.<String>builder()
                .statusCode(500)
                .build();
        
        assertFalse(response.isSuccess());
        assertFalse(response.isClientError());
        assertTrue(response.isServerError());
    }
}