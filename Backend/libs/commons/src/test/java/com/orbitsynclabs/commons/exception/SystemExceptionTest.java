package com.orbitsynclabs.commons.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for SystemException
 */
class SystemExceptionTest {

    @Test
    void testSystemExceptionWithErrorCodeAndMessage() {
        String errorCode = "SYS_001";
        String message = "Test system error";
        
        SystemException exception = new SystemException(errorCode, message);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getTechnicalDetails());
        assertNull(exception.getCause());
    }

    @Test
    void testSystemExceptionWithErrorCodeMessageAndTechnicalDetails() {
        String errorCode = "SYS_002";
        String message = "Test system error with technical details";
        String technicalDetails = "Stack trace: NullPointerException at line 123";
        
        SystemException exception = new SystemException(errorCode, message, technicalDetails);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertEquals(technicalDetails, exception.getTechnicalDetails());
        assertNull(exception.getCause());
    }

    @Test
    void testSystemExceptionWithErrorCodeMessageAndCause() {
        String errorCode = "SYS_003";
        String message = "Test system error with cause";
        Throwable cause = new RuntimeException("Database connection failed");
        
        SystemException exception = new SystemException(errorCode, message, cause);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getTechnicalDetails());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testSystemExceptionExtendsRuntimeException() {
        SystemException exception = new SystemException("SYS_004", "Test error");
        
        assertThrows(RuntimeException.class, () -> {
            throw exception;
        });
    }
}