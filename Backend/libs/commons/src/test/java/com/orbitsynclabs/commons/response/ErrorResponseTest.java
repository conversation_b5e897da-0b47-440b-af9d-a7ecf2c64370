package com.orbitsynclabs.commons.response;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Test class for ErrorResponse
 */
class ErrorResponseTest {

    @Test
    void testBadRequestResponse() {
        String message = "Invalid request parameters";
        String errorCode = "VAL_001";
        
        ErrorResponse response = ErrorResponse.badRequest(message, errorCode);
        
        assertEquals("Bad Request", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(400, response.getStatusCode());
        assertNotNull(response.getRequestId());
        assertNotNull(response.getTimestamp());
        assertFalse(response.hasValidationErrors());
        assertFalse(response.hasDebugInfo());
    }

    @Test
    void testBadRequestResponseWithValidationErrors() {
        String message = "Validation failed";
        String errorCode = "VAL_002";
        List<ValidationError> validationErrors = List.of(
            ValidationError.of("field1", "Field1 is required"),
            ValidationError.of("field2", "Field2 must be a valid email")
        );
        
        ErrorResponse response = ErrorResponse.badRequest(message, errorCode, validationErrors);
        
        assertEquals("Bad Request", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(400, response.getStatusCode());
        assertTrue(response.hasValidationErrors());
        assertEquals(2, response.getValidationErrors().size());
    }

    @Test
    void testUnauthorizedResponse() {
        String message = "Authentication required";
        String errorCode = "SEC_001";
        
        ErrorResponse response = ErrorResponse.unauthorized(message, errorCode);
        
        assertEquals("Unauthorized", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(401, response.getStatusCode());
    }

    @Test
    void testForbiddenResponse() {
        String message = "Access denied";
        String errorCode = "SEC_005";
        
        ErrorResponse response = ErrorResponse.forbidden(message, errorCode);
        
        assertEquals("Forbidden", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(403, response.getStatusCode());
    }

    @Test
    void testNotFoundResponse() {
        String message = "Resource not found";
        String errorCode = "BUS_001";
        
        ErrorResponse response = ErrorResponse.notFound(message, errorCode);
        
        assertEquals("Not Found", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(404, response.getStatusCode());
    }

    @Test
    void testConflictResponse() {
        String message = "Resource already exists";
        String errorCode = "BUS_002";
        
        ErrorResponse response = ErrorResponse.conflict(message, errorCode);
        
        assertEquals("Conflict", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(409, response.getStatusCode());
    }

    @Test
    void testUnprocessableEntityResponse() {
        String message = "Invalid entity data";
        String errorCode = "VAL_003";
        
        ErrorResponse response = ErrorResponse.unprocessableEntity(message, errorCode);
        
        assertEquals("Unprocessable Entity", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(422, response.getStatusCode());
    }

    @Test
    void testInternalServerErrorResponse() {
        String message = "Internal server error";
        String errorCode = "SYS_001";
        
        ErrorResponse response = ErrorResponse.internalServerError(message, errorCode);
        
        assertEquals("Internal Server Error", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(500, response.getStatusCode());
    }

    @Test
    void testServiceUnavailableResponse() {
        String message = "Service temporarily unavailable";
        String errorCode = "EXT_001";
        
        ErrorResponse response = ErrorResponse.serviceUnavailable(message, errorCode);
        
        assertEquals("Service Unavailable", response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(503, response.getStatusCode());
    }

    @Test
    void testCustomResponse() {
        String error = "Custom Error";
        String message = "Custom error message";
        String errorCode = "CUSTOM_001";
        int statusCode = 418;
        
        ErrorResponse response = ErrorResponse.custom(error, message, errorCode, statusCode);
        
        assertEquals(error, response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(statusCode, response.getStatusCode());
    }

    @Test
    void testCustomResponseWithDebugInfo() {
        String error = "Custom Error";
        String message = "Custom error message";
        String errorCode = "CUSTOM_002";
        int statusCode = 418;
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("stackTrace", "Exception at line 123");
        debugInfo.put("additionalInfo", "Some additional info");
        
        ErrorResponse response = ErrorResponse.custom(error, message, errorCode, statusCode, debugInfo);
        
        assertEquals(error, response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(statusCode, response.getStatusCode());
        assertTrue(response.hasDebugInfo());
        assertEquals(debugInfo, response.getDebugInfo());
    }

    @Test
    void testCustomResponseWithValidationErrors() {
        String error = "Validation Error";
        String message = "Custom validation error";
        String errorCode = "VAL_004";
        int statusCode = 422;
        List<ValidationError> validationErrors = List.of(
            ValidationError.of("field1", "Field1 error"),
            ValidationError.of("field2", "Field2 error")
        );
        
        ErrorResponse response = ErrorResponse.custom(error, message, errorCode, statusCode, validationErrors);
        
        assertEquals(error, response.getError());
        assertEquals(message, response.getMessage());
        assertEquals(errorCode, response.getErrorCode());
        assertEquals(statusCode, response.getStatusCode());
        assertTrue(response.hasValidationErrors());
        assertEquals(validationErrors, response.getValidationErrors());
    }

    @Test
    void testWithDebugInfo() {
        ErrorResponse response = ErrorResponse.badRequest("Test error", "VAL_001");
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("key1", "value1");
        
        response = response.withDebugInfo(debugInfo);
        
        assertTrue(response.hasDebugInfo());
        assertEquals(debugInfo, response.getDebugInfo());
    }

    @Test
    void testWithDebugInfoKeyValue() {
        ErrorResponse response = ErrorResponse.badRequest("Test error", "VAL_001");
        
        response = response.withDebugInfo("key", "value");
        
        assertTrue(response.hasDebugInfo());
        assertEquals("value", response.getDebugInfo().get("key"));
    }

    @Test
    void testWithValidationErrors() {
        ErrorResponse response = ErrorResponse.badRequest("Test error", "VAL_001");
        List<ValidationError> validationErrors = List.of(
            ValidationError.of("field1", "Field1 error")
        );
        
        response = response.withValidationErrors(validationErrors);
        
        assertTrue(response.hasValidationErrors());
        assertEquals(validationErrors, response.getValidationErrors());
    }

    @Test
    void testWithValidationError() {
        ErrorResponse response = ErrorResponse.badRequest("Test error", "VAL_001");
        ValidationError validationError = ValidationError.of("field1", "Field1 error");
        
        response = response.withValidationError(validationError);
        
        assertTrue(response.hasValidationErrors());
        assertEquals(1, response.getValidationErrors().size());
        assertEquals(validationError, response.getValidationErrors().get(0));
    }

    @Test
    void testRequestIdsAreUnique() {
        ErrorResponse response1 = ErrorResponse.badRequest("Error 1", "VAL_001");
        ErrorResponse response2 = ErrorResponse.badRequest("Error 2", "VAL_002");
        
        assertNotEquals(response1.getRequestId(), response2.getRequestId());
    }

    @Test
    void testBuilderPattern() {
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("key1", "value1");
        
        ErrorResponse response = ErrorResponse.builder()
                .error("Custom Error")
                .message("Custom message")
                .errorCode("CUSTOM_001")
                .statusCode(418)
                .requestId(UUID.randomUUID().toString())
                .timestamp(LocalDateTime.now())
                .debugInfo(debugInfo)
                .build();
        
        assertEquals("Custom Error", response.getError());
        assertEquals("Custom message", response.getMessage());
        assertEquals("CUSTOM_001", response.getErrorCode());
        assertEquals(418, response.getStatusCode());
        assertEquals(debugInfo, response.getDebugInfo());
    }
}