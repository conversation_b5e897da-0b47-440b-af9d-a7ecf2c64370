package com.orbitsynclabs.commons.logging;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for LogContext
 */
class LogContextTest {

    private LogContext logContext;

    @BeforeEach
    void setUp() {
        logContext = new LogContext();
        logContext.clearContext();
    }

    @Test
    void testSetAndGetCorrelationId() {
        String correlationId = "test-correlation-id";
        
        logContext.setCorrelationId(correlationId);
        
        assertEquals(correlationId, logContext.getCorrelationId());
    }

    @Test
    void testGenerateAndSetCorrelationId() {
        String correlationId = logContext.generateAndSetCorrelationId();
        
        assertNotNull(correlationId);
        assertEquals(correlationId, logContext.getCorrelationId());
    }

    @Test
    void testSetAndGetUserId() {
        String userId = "test-user-id";
        
        logContext.setUserId(userId);
        
        assertEquals(userId, logContext.getUserId());
    }

    @Test
    void testSetAndGetRequestId() {
        String requestId = "test-request-id";
        
        logContext.setRequestId(requestId);
        
        assertEquals(requestId, logContext.getRequestId());
    }

    @Test
    void testSetAndGetServiceName() {
        String serviceName = "test-service";
        
        logContext.setServiceName(serviceName);
        
        assertEquals(serviceName, logContext.getServiceName());
    }

    @Test
    void testSetAndGetEnvironment() {
        String environment = "test-env";
        
        logContext.setEnvironment(environment);
        
        assertEquals(environment, logContext.getEnvironment());
    }

    @Test
    void testSetAndGetContextValue() {
        String key = "custom-key";
        String value = "custom-value";
        
        logContext.setContextValue(key, value);
        
        assertEquals(value, logContext.getContextValue(key));
    }

    @Test
    void testGetAllContext() {
        logContext.setCorrelationId("corr-id");
        logContext.setUserId("user-id");
        logContext.setContextValue("custom-key", "custom-value");
        
        Map<String, String> allContext = logContext.getAllContext();
        
        assertEquals(3, allContext.size());
        assertEquals("corr-id", allContext.get("correlationId"));
        assertEquals("user-id", allContext.get("userId"));
        assertEquals("custom-value", allContext.get("custom-key"));
    }

    @Test
    void testClearContext() {
        logContext.setCorrelationId("test-id");
        logContext.setUserId("test-user");
        
        assertTrue(logContext.hasContext());
        
        logContext.clearContext();
        
        assertFalse(logContext.hasContext());
        assertNull(logContext.getCorrelationId());
        assertNull(logContext.getUserId());
    }

    @Test
    void testHasContext() {
        assertFalse(logContext.hasContext());
        
        logContext.setCorrelationId("test-id");
        
        assertTrue(logContext.hasContext());
    }

    @Test
    void testCreateContext() {
        String correlationId = "test-correlation-id";
        String userId = "test-user-id";
        String requestId = "test-request-id";
        String serviceName = "test-service";
        String environment = "test-env";
        
        logContext.createContext(correlationId, userId, requestId, serviceName, environment);
        
        assertEquals(correlationId, logContext.getCorrelationId());
        assertEquals(userId, logContext.getUserId());
        assertEquals(requestId, logContext.getRequestId());
        assertEquals(serviceName, logContext.getServiceName());
        assertEquals(environment, logContext.getEnvironment());
    }

    @Test
    void testCreateContextWithAutoGeneratedCorrelationId() {
        String userId = "test-user-id";
        String requestId = "test-request-id";
        String serviceName = "test-service";
        String environment = "test-env";
        
        logContext.createContext(userId, requestId, serviceName, environment);
        
        assertNotNull(logContext.getCorrelationId());
        assertEquals(userId, logContext.getUserId());
        assertEquals(requestId, logContext.getRequestId());
        assertEquals(serviceName, logContext.getServiceName());
        assertEquals(environment, logContext.getEnvironment());
    }

    @Test
    void testCopyContext() {
        LogContext sourceContext = new LogContext();
        sourceContext.setCorrelationId("source-correlation-id");
        sourceContext.setUserId("source-user-id");
        sourceContext.setContextValue("source-key", "source-value");
        
        logContext.copyContext(sourceContext);
        
        assertEquals("source-correlation-id", logContext.getCorrelationId());
        assertEquals("source-user-id", logContext.getUserId());
        assertEquals("source-value", logContext.getContextValue("source-key"));
    }

    @Test
    void testContextIsolationBetweenThreads() {
        // This test would require threading, but we can at least verify that
        // the context is properly isolated within the same thread
        logContext.setCorrelationId("thread-1-id");
        
        LogContext anotherContext = new LogContext();
        anotherContext.setCorrelationId("thread-2-id");
        
        assertEquals("thread-1-id", logContext.getCorrelationId());
        assertEquals("thread-2-id", anotherContext.getCorrelationId());
    }

    @Test
    void testMultipleContextValues() {
        logContext.setContextValue("key1", "value1");
        logContext.setContextValue("key2", "value2");
        logContext.setContextValue("key3", "value3");
        
        assertEquals("value1", logContext.getContextValue("key1"));
        assertEquals("value2", logContext.getContextValue("key2"));
        assertEquals("value3", logContext.getContextValue("key3"));
    }

    @Test
    void testOverwriteContextValue() {
        logContext.setContextValue("key", "original-value");
        logContext.setContextValue("key", "new-value");
        
        assertEquals("new-value", logContext.getContextValue("key"));
    }

    @Test
    void testNullContextValues() {
        logContext.setContextValue("null-key", null);
        
        assertNull(logContext.getContextValue("null-key"));
    }
}