package com.orbitsynclabs.commons.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for BusinessException
 */
class BusinessExceptionTest {

    @Test
    void testBusinessExceptionWithErrorCodeAndMessage() {
        String errorCode = "BUS_001";
        String message = "Test business error";
        
        BusinessException exception = new BusinessException(errorCode, message);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getDetails());
        assertNull(exception.getCause());
    }

    @Test
    void testBusinessExceptionWithErrorCodeMessageAndDetails() {
        String errorCode = "BUS_002";
        String message = "Test business error with details";
        String details = "Additional details about the error";
        
        BusinessException exception = new BusinessException(errorCode, message, details);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertEquals(details, exception.getDetails());
        assertNull(exception.getCause());
    }

    @Test
    void testBusinessExceptionWithErrorCodeMessageAndCause() {
        String errorCode = "BUS_003";
        String message = "Test business error with cause";
        Throwable cause = new RuntimeException("Root cause");
        
        BusinessException exception = new BusinessException(errorCode, message, cause);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getDetails());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testBusinessExceptionExtendsRuntimeException() {
        BusinessException exception = new BusinessException("BUS_004", "Test error");
        
        assertThrows(RuntimeException.class, () -> {
            throw exception;
        });
    }
}