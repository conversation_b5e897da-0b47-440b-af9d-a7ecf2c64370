package com.orbitsynclabs.commons.exception;

import com.orbitsynclabs.commons.response.ErrorResponse;
import com.orbitsynclabs.commons.response.ValidationError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test class for GlobalExceptionHandler
 */
@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Mock
    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        request.setRequestURI("/test");
        request.addHeader("X-Request-ID", "test-request-id");
    }

    @Test
    void testHandleBusinessException() {
        BusinessException ex = new BusinessException("BUS_001", "Test business error");
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleBusinessException(ex, request);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Business Error", errorResponse.getError());
        assertEquals("Test business error", errorResponse.getMessage());
        assertEquals("BUS_001", errorResponse.getErrorCode());
        assertEquals(400, errorResponse.getStatusCode());
        assertEquals("test-request-id", errorResponse.getRequestId());
        assertNotNull(errorResponse.getTimestamp());
        assertNotNull(errorResponse.getDebugInfo());
    }

    @Test
    void testHandleSystemException() {
        SystemException ex = new SystemException("SYS_001", "Test system error");
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleSystemException(ex, request);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("System Error", errorResponse.getError());
        assertEquals("An internal system error occurred", errorResponse.getMessage());
        assertEquals("SYS_001", errorResponse.getErrorCode());
        assertEquals(500, errorResponse.getStatusCode());
    }

    @Test
    void testHandleMethodArgumentNotValidException() {
        MethodArgumentNotValidException ex = new MethodArgumentNotValidException(
            null, 
            createBindingResult()
        );
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleMethodArgumentNotValidException(ex, request);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Validation Error", errorResponse.getError());
        assertEquals("Input validation failed", errorResponse.getMessage());
        assertEquals("VAL_002", errorResponse.getErrorCode());
        assertEquals(400, errorResponse.getStatusCode());
        assertNotNull(errorResponse.getValidationErrors());
        assertFalse(errorResponse.getValidationErrors().isEmpty());
    }

    @Test
    void testHandleAuthenticationException() {
        org.springframework.security.core.AuthenticationException ex = 
            new org.springframework.security.core.AuthenticationException("Authentication failed") {};
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleAuthenticationException(ex, request);
        
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Authentication Error", errorResponse.getError());
        assertEquals("Authentication failed", errorResponse.getMessage());
        assertEquals("SEC_001", errorResponse.getErrorCode());
        assertEquals(401, errorResponse.getStatusCode());
    }

    @Test
    void testHandleAccessDeniedException() {
        org.springframework.security.access.AccessDeniedException ex = 
            new org.springframework.security.access.AccessDeniedException("Access denied");
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleAccessDeniedException(ex, request);
        
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Authorization Error", errorResponse.getError());
        assertEquals("Access denied", errorResponse.getMessage());
        assertEquals("SEC_005", errorResponse.getErrorCode());
        assertEquals(403, errorResponse.getStatusCode());
    }

    @Test
    void testHandleNoHandlerFoundException() {
        NoHandlerFoundException ex = new NoHandlerFoundException("GET", "/test", null);
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleNoHandlerFoundException(ex, request);
        
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Not Found", errorResponse.getError());
        assertEquals("The requested resource was not found", errorResponse.getMessage());
        assertEquals("BUS_001", errorResponse.getErrorCode());
        assertEquals(404, errorResponse.getStatusCode());
    }

    @Test
    void testHandleMissingServletRequestParameterException() {
        MissingServletRequestParameterException ex = 
            new MissingServletRequestParameterException("param", "String");
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleMissingServletRequestParameterException(ex, request);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Bad Request", errorResponse.getError());
        assertEquals("Required parameter 'param' is missing", errorResponse.getMessage());
        assertEquals("VAL_001", errorResponse.getErrorCode());
        assertEquals(400, errorResponse.getStatusCode());
    }

    @Test
    void testHandleMethodArgumentTypeMismatchException() {
        MethodArgumentTypeMismatchException ex = 
            new MethodArgumentTypeMismatchException("param", String.class, "value", null, null);
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleMethodArgumentTypeMismatchException(ex, request);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Bad Request", errorResponse.getError());
        assertEquals("Parameter 'param' has invalid type", errorResponse.getMessage());
        assertEquals("VAL_002", errorResponse.getErrorCode());
        assertEquals(400, errorResponse.getStatusCode());
    }

    @Test
    void testHandleGenericException() {
        Exception ex = new RuntimeException("Generic error");
        
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleGenericException(ex, request);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals("Internal Server Error", errorResponse.getError());
        assertEquals("An unexpected error occurred", errorResponse.getMessage());
        assertEquals("SYS_001", errorResponse.getErrorCode());
        assertEquals(500, errorResponse.getStatusCode());
        assertNotNull(errorResponse.getDebugInfo());
    }

    @Test
    void testGenerateRequestIdFromHeader() {
        request.addHeader("X-Request-ID", "custom-request-id");
        
        BusinessException ex = new BusinessException("BUS_001", "Test error");
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleBusinessException(ex, request);
        
        assertEquals("custom-request-id", response.getBody().getRequestId());
    }

    @Test
    void testGenerateRequestIdAutomatically() {
        BusinessException ex = new BusinessException("BUS_001", "Test error");
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleBusinessException(ex, request);
        
        assertNotNull(response.getBody().getRequestId());
        assertNotEquals("test-request-id", response.getBody().getRequestId());
    }

    private BindingResult createBindingResult() {
        BindingResult bindingResult = mock(BindingResult.class);
        FieldError fieldError = new FieldError("object", "field", "Default message");
        when(bindingResult.getFieldErrors()).thenReturn(List.of(fieldError));
        return bindingResult;
    }
}