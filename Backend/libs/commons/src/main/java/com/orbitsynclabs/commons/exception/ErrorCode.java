package src.main.java.com.orbitsynclabs.commons.exception;

/**
 * Centralized error codes catalog
 */
public final class ErrorCode {
    
    // System Error Codes (SYS_*)
    public static final String SYS_INTERNAL_ERROR = "SYS_001";
    public static final String SYS_DATABASE_ERROR = "SYS_002";
    public static final String SYS_NETWORK_ERROR = "SYS_003";
    public static final String SYS_FILE_IO_ERROR = "SYS_004";
    public static final String SYS_CONFIGURATION_ERROR = "SYS_005";
    
    // Business Error Codes (BUS_*)
    public static final String BUS_RESOURCE_NOT_FOUND = "BUS_001";
    public static final String BUS_RESOURCE_ALREADY_EXISTS = "BUS_002";
    public static final String BUS_INVALID_INPUT = "BUS_003";
    public static final String BUS_UNAUTHORIZED_ACCESS = "BUS_004";
    public static final String BUS_FORBIDDEN_ACCESS = "BUS_005";
    public static final String BUS_BUSINESS_RULE_VIOLATION = "BUS_006";
    public static final String BUS_INSUFFICIENT_BALANCE = "BUS_007";
    public static final String BUS_EXPIRED_TOKEN = "BUS_008";
    
    // Validation Error Codes (VAL_*)
    public static final String VAL_REQUIRED_FIELD = "VAL_001";
    public static final String VAL_INVALID_FORMAT = "VAL_002";
    public static final String VAL_INVALID_LENGTH = "VAL_003";
    public static final String VAL_INVALID_RANGE = "VAL_004";
    public static final String VAL_INVALID_PATTERN = "VAL_005";
    public static final String VAL_DUPLICATE_VALUE = "VAL_006";
    
    // Security Error Codes (SEC_*)
    public static final String SEC_AUTHENTICATION_FAILED = "SEC_001";
    public static final String SEC_AUTHORIZATION_FAILED = "SEC_002";
    public static final String SEC_TOKEN_EXPIRED = "SEC_003";
    public static final String SEC_TOKEN_INVALID = "SEC_004";
    public static final String SEC_PERMISSION_DENIED = "SEC_005";
    
    // External Service Error Codes (EXT_*)
    public static final String EXT_SERVICE_UNAVAILABLE = "EXT_001";
    public static final String EXT_SERVICE_TIMEOUT = "EXT_002";
    public static final String EXT_SERVICE_RATE_LIMITED = "EXT_003";
    public static final String EXT_SERVICE_INVALID_RESPONSE = "EXT_004";
    
    private ErrorCode() {
        // Private constructor to prevent instantiation
    }
}