package com.orbitsynclabs.commons.logging;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Thread-safe logging context manager
 */
@Component
@Slf4j
public class LogContext {
    
    private static final ThreadLocal<Map<String, String>> contextHolder = ThreadLocal.withInitial(ConcurrentHashMap::new);
    
    /**
     * Set correlation ID for current thread
     */
    public void setCorrelationId(String correlationId) {
        getContext().put("correlationId", correlationId);
    }
    
    /**
     * Get correlation ID for current thread
     */
    public String getCorrelationId() {
        return getContext().get("correlationId");
    }
    
    /**
     * Generate and set new correlation ID
     */
    public String generateAndSetCorrelationId() {
        String correlationId = UUID.randomUUID().toString();
        setCorrelationId(correlationId);
        return correlationId;
    }
    
    /**
     * Set user ID for current thread
     */
    public void setUserId(String userId) {
        getContext().put("userId", userId);
    }
    
    /**
     * Get user ID for current thread
     */
    public String getUserId() {
        return getContext().get("userId");
    }
    
    /**
     * Set request ID for current thread
     */
    public void setRequestId(String requestId) {
        getContext().put("requestId", requestId);
    }
    
    /**
     * Get request ID for current thread
     */
    public String getRequestId() {
        return getContext().get("requestId");
    }
    
    /**
     * Set service name for current thread
     */
    public void setServiceName(String serviceName) {
        getContext().put("serviceName", serviceName);
    }
    
    /**
     * Get service name for current thread
     */
    public String getServiceName() {
        return getContext().get("serviceName");
    }
    
    /**
     * Set environment for current thread
     */
    public void setEnvironment(String environment) {
        getContext().put("environment", environment);
    }
    
    /**
     * Get environment for current thread
     */
    public String getEnvironment() {
        return getContext().get("environment");
    }
    
    /**
     * Set custom context value
     */
    public void setContextValue(String key, String value) {
        getContext().put(key, value);
    }
    
    /**
     * Get custom context value
     */
    public String getContextValue(String key) {
        return getContext().get(key);
    }
    
    /**
     * Get all context values for current thread
     */
    public Map<String, String> getAllContext() {
        return new ConcurrentHashMap<>(getContext());
    }
    
    /**
     * Clear all context for current thread
     */
    public void clearContext() {
        contextHolder.remove();
    }
    
    /**
     * Check if context exists for current thread
     */
    public boolean hasContext() {
        return contextHolder.get() != null && !contextHolder.get().isEmpty();
    }
    
    /**
     * Get context map for current thread
     */
    private Map<String, String> getContext() {
        return contextHolder.get();
    }
    
    /**
     * Create a new context with correlation ID
     */
    public void createContext(String correlationId, String userId, String requestId, 
                            String serviceName, String environment) {
        Map<String, String> context = getContext();
        context.put("correlationId", correlationId);
        context.put("userId", userId);
        context.put("requestId", requestId);
        context.put("serviceName", serviceName);
        context.put("environment", environment);
        
        log.debug("Logging context created. Correlation ID: {}, User ID: {}, Service: {}", 
                 correlationId, userId, serviceName);
    }
    
    /**
     * Create a new context with auto-generated correlation ID
     */
    public void createContext(String userId, String requestId, String serviceName, String environment) {
        String correlationId = UUID.randomUUID().toString();
        createContext(correlationId, userId, requestId, serviceName, environment);
    }
    
    /**
     * Copy context from another thread
     */
    public void copyContext(LogContext sourceContext) {
        Map<String, String> source = sourceContext.getAllContext();
        Map<String, String> target = getContext();
        target.clear();
        target.putAll(source);
        
        log.debug("Logging context copied from source. Correlation ID: {}", getCorrelationId());
    }
}