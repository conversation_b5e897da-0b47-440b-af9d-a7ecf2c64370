package com.orbitsynclabs.commons.logging;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

/**
 * Enterprise-grade structured logger with correlation ID support
 */
@Component
@Slf4j
public class StructuredLogger {
    
    private static final String CORRELATION_ID = "correlationId";
    private static final String USER_ID = "userId";
    private static final String REQUEST_ID = "requestId";
    private static final String SERVICE_NAME = "serviceName";
    private static final String ENVIRONMENT = "environment";
    
    /**
     * Create a logger for a specific class
     */
    public Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }
    
    /**
     * Start a new logging context with correlation ID
     */
    public void startContext(String userId) {
        String correlationId = UUID.randomUUID().toString();
        MDC.put(CORRELATION_ID, correlationId);
        MDC.put(USER_ID, userId);
        MDC.put(REQUEST_ID, correlationId);
        MDC.put(SERVICE_NAME, "orbitsynclabs-commons");
        MDC.put(ENVIRONMENT, System.getProperty("spring.profiles.active", "unknown"));
        
        log.info("Logging context started. Correlation ID: {}, User ID: {}", correlationId, userId);
    }
    
    /**
     * Start a new logging context with existing correlation ID
     */
    public void startContext(String correlationId, String userId) {
        MDC.put(CORRELATION_ID, correlationId);
        MDC.put(USER_ID, userId);
        MDC.put(REQUEST_ID, correlationId);
        MDC.put(SERVICE_NAME, "orbitsynclabs-commons");
        MDC.put(ENVIRONMENT, System.getProperty("spring.profiles.active", "unknown"));
        
        log.info("Logging context started. Correlation ID: {}, User ID: {}", correlationId, userId);
    }
    
    /**
     * Clear logging context
     */
    public void clearContext() {
        MDC.remove(CORRELATION_ID);
        MDC.remove(USER_ID);
        MDC.remove(REQUEST_ID);
        MDC.remove(SERVICE_NAME);
        MDC.remove(ENVIRONMENT);
        
        log.info("Logging context cleared");
    }
    
    /**
     * Log business operation with structured data
     */
    public void logBusinessOperation(Logger logger, String operation, String status, 
                                   Map<String, Object> metadata) {
        logger.info("Business operation - Operation: {}, Status: {}, Metadata: {}", 
                   operation, status, metadata);
    }
    
    /**
     * Log API request with structured data
     */
    public void logApiRequest(Logger logger, String method, String path, String userAgent, 
                            Map<String, Object> headers) {
        logger.info("API request - Method: {}, Path: {}, User-Agent: {}, Headers: {}", 
                   method, path, userAgent, headers);
    }
    
    /**
     * Log API response with structured data
     */
    public void logApiResponse(Logger logger, String method, String path, int statusCode, 
                             long responseTime, Map<String, Object> response) {
        logger.info("API response - Method: {}, Path: {}, Status: {}, ResponseTime: {}ms, Response: {}", 
                   method, path, statusCode, responseTime, response);
    }
    
    /**
     * Log security event with structured data
     */
    public void logSecurityEvent(Logger logger, String eventType, String userId, 
                               String resource, String action, boolean success) {
        logger.warn("Security event - Type: {}, User: {}, Resource: {}, Action: {}, Success: {}", 
                   eventType, userId, resource, action, success);
    }
    
    /**
     * Log performance metric with structured data
     */
    public void logPerformanceMetric(Logger logger, String metricName, double value, 
                                   Map<String, Object> tags) {
        logger.info("Performance metric - Name: {}, Value: {}, Tags: {}", 
                   metricName, value, tags);
    }
    
    /**
     * Log error with structured data
     */
    public void logError(Logger logger, String errorType, String errorMessage, 
                        Map<String, Object> context, Throwable throwable) {
        logger.error("Error occurred - Type: {}, Message: {}, Context: {}", 
                    errorType, errorMessage, context, throwable);
    }
    
    /**
     * Log audit event with structured data
     */
    public void logAuditEvent(Logger logger, String eventType, String userId, 
                            String action, Map<String, Object> details) {
        logger.info("Audit event - Type: {}, User: {}, Action: {}, Details: {}", 
                   eventType, userId, action, details);
    }
    
    /**
     * Log system event with structured data
     */
    public void logSystemEvent(Logger logger, String eventType, String component, 
                             Map<String, Object> details) {
        logger.info("System event - Type: {}, Component: {}, Details: {}", 
                   eventType, component, details);
    }
    
    /**
     * Log database operation with structured data
     */
    public void logDatabaseOperation(Logger logger, String operation, String table, 
                                   Map<String, Object> conditions, long executionTime) {
        logger.info("Database operation - Operation: {}, Table: {}, Conditions: {}, ExecutionTime: {}ms", 
                   operation, table, conditions, executionTime);
    }
    
    /**
     * Log external service call with structured data
     */
    public void logExternalServiceCall(Logger logger, String serviceName, String endpoint, 
                                     Map<String, Object> request, Map<String, Object> response, 
                                     long responseTime, boolean success) {
        logger.info("External service call - Service: {}, Endpoint: {}, Request: {}, Response: {}, " +
                   "ResponseTime: {}ms, Success: {}", 
                   serviceName, endpoint, request, response, responseTime, success);
    }
}