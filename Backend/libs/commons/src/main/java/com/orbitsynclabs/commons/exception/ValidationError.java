package com.orbitsynclabs.commons.exception;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Represents validation error details
 */
@Data
@NoArgsConstructor
public class ValidationError {
    
    private String field;
    private String message;
    private String rejectedValue;
    
    public ValidationError(String field, String message) {
        this.field = field;
        this.message = message;
    }
    
    public ValidationError(String field, String message, String rejectedValue) {
        this.field = field;
        this.message = message;
        this.rejectedValue = rejectedValue;
    }
    
    /**
     * Convert Spring validation errors to ValidationError list
     */
    public static List<ValidationError> fromBindingErrors(org.springframework.validation.BindingResult bindingResult) {
        return bindingResult.getFieldErrors().stream()
                .map(error -> new ValidationError(
                        error.getField(),
                        error.getDefaultMessage(),
                        error.getRejectedValue() != null ? error.getRejectedValue().toString() : null
                ))
                .collect(Collectors.toList());
    }
}