package com.orbitsynclabs.commons.response;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * Utility class for creating standardized responses
 */
public final class ResponseUtils {
    
    private ResponseUtils() {
        // Private constructor to prevent instantiation
    }
    
    /**
     * Create a successful response entity
     */
    public static <T> ResponseEntity<BaseResponse<T>> success(T data) {
        return ResponseEntity.ok(BaseResponse.success(data));
    }
    
    /**
     * Create a successful response entity with custom message
     */
    public static <T> ResponseEntity<BaseResponse<T>> success(T data, String message) {
        return ResponseEntity.ok(BaseResponse.success(data, message));
    }
    
    /**
     * Create a successful response entity with custom message and metadata
     */
    public static <T> ResponseEntity<BaseResponse<T>> success(T data, String message, Map<String, Object> metadata) {
        return ResponseEntity.ok(BaseResponse.success(data, message, metadata));
    }
    
    /**
     * Create a successful response entity without data
     */
    public static <T> ResponseEntity<BaseResponse<T>> success(String message) {
        return ResponseEntity.ok(BaseResponse.success(message));
    }
    
    /**
     * Create a created response entity (HTTP 201)
     */
    public static <T> ResponseEntity<BaseResponse<T>> created(T data, String message) {
        return ResponseEntity.status(HttpStatus.CREATED).body(BaseResponse.created(data, message));
    }
    
    /**
     * Create an accepted response entity (HTTP 202)
     */
    public static <T> ResponseEntity<BaseResponse<T>> accepted(T data, String message) {
        return ResponseEntity.status(HttpStatus.ACCEPTED).body(BaseResponse.accepted(data, message));
    }
    
    /**
     * Create a no content response entity (HTTP 204)
     */
    public static <T> ResponseEntity<BaseResponse<T>> noContent(String message) {
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(BaseResponse.noContent(message));
    }
    
    /**
     * Create a bad request response entity
     */
    public static ResponseEntity<ErrorResponse> badRequest(String message, String errorCode) {
        return ResponseEntity.badRequest().body(ErrorResponse.badRequest(message, errorCode));
    }
    
    /**
     * Create a bad request response entity with validation errors
     */
    public static ResponseEntity<ErrorResponse> badRequest(String message, String errorCode, 
                                                        List<ValidationError> validationErrors) {
        return ResponseEntity.badRequest().body(ErrorResponse.badRequest(message, errorCode, validationErrors));
    }
    
    /**
     * Create an unauthorized response entity
     */
    public static ResponseEntity<ErrorResponse> unauthorized(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ErrorResponse.unauthorized(message, errorCode));
    }
    
    /**
     * Create a forbidden response entity
     */
    public static ResponseEntity<ErrorResponse> forbidden(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(ErrorResponse.forbidden(message, errorCode));
    }
    
    /**
     * Create a not found response entity
     */
    public static ResponseEntity<ErrorResponse> notFound(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ErrorResponse.notFound(message, errorCode));
    }
    
    /**
     * Create a conflict response entity
     */
    public static ResponseEntity<ErrorResponse> conflict(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.CONFLICT).body(ErrorResponse.conflict(message, errorCode));
    }
    
    /**
     * Create an unprocessable entity response entity
     */
    public static ResponseEntity<ErrorResponse> unprocessableEntity(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(ErrorResponse.unprocessableEntity(message, errorCode));
    }
    
    /**
     * Create an internal server error response entity
     */
    public static ResponseEntity<ErrorResponse> internalServerError(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ErrorResponse.internalServerError(message, errorCode));
    }
    
    /**
     * Create a service unavailable response entity
     */
    public static ResponseEntity<ErrorResponse> serviceUnavailable(String message, String errorCode) {
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(ErrorResponse.serviceUnavailable(message, errorCode));
    }
    
    /**
     * Create a custom response entity
     */
    public static ResponseEntity<ErrorResponse> custom(String error, String message, String errorCode, 
                                                    int statusCode) {
        return ResponseEntity.status(statusCode).body(ErrorResponse.custom(error, message, errorCode, statusCode));
    }
    
    /**
     * Create a custom response entity with debug info
     */
    public static ResponseEntity<ErrorResponse> custom(String error, String message, String errorCode, 
                                                    int statusCode, Map<String, Object> debugInfo) {
        return ResponseEntity.status(statusCode).body(ErrorResponse.custom(error, message, errorCode, statusCode, debugInfo));
    }
    
    /**
     * Create a custom response entity with validation errors
     */
    public static ResponseEntity<ErrorResponse> custom(String error, String message, String errorCode, 
                                                    int statusCode, List<ValidationError> validationErrors) {
        return ResponseEntity.status(statusCode).body(ErrorResponse.custom(error, message, errorCode, statusCode, validationErrors));
    }
}