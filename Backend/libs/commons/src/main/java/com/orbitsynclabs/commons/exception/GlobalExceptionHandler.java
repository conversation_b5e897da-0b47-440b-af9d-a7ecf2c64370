package com.orbitsynclabs.commons.exception;

import com.orbitsynclabs.commons.response.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Global exception handler for centralized error management
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final String REQUEST_ID_HEADER = "X-Request-ID";

    /**
     * Handle business exceptions
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(
            BusinessException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("Business exception occurred. Request ID: {}, Error Code: {}, Message: {}", 
                requestId, ex.getErrorCode(), ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Business Error")
                .message(ex.getMessage())
                .errorCode(ex.getErrorCode())
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "details", ex.getDetails()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle system exceptions
     */
    @ExceptionHandler(SystemException.class)
    public ResponseEntity<ErrorResponse> handleSystemException(
            SystemException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.error("System exception occurred. Request ID: {}, Error Code: {}, Message: {}", 
                requestId, ex.getErrorCode(), ex.getMessage(), ex);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("System Error")
                .message("An internal system error occurred")
                .errorCode(ex.getErrorCode())
                .statusCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "technicalDetails", ex.getTechnicalDetails()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Handle validation exceptions (MethodArgumentNotValidException)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        List<ValidationError> validationErrors = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> new ValidationError(
                        error.getField(),
                        error.getDefaultMessage(),
                        error.getRejectedValue() != null ? error.getRejectedValue().toString() : null
                ))
                .toList();
        
        log.warn("Validation exception occurred. Request ID: {}, Field errors: {}", 
                requestId, validationErrors.size());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Validation Error")
                .message("Input validation failed")
                .errorCode(ErrorCode.VAL_INVALID_FORMAT)
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .validationErrors(validationErrors)
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "fieldCount", validationErrors.size()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle binding exceptions
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ErrorResponse> handleBindException(
            BindException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        List<ValidationError> validationErrors = ValidationError.fromBindingErrors(ex);
        
        log.warn("Bind exception occurred. Request ID: {}, Validation errors: {}", 
                requestId, validationErrors.size());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Validation Error")
                .message("Request binding failed")
                .errorCode(ErrorCode.VAL_INVALID_FORMAT)
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .validationErrors(validationErrors)
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle authentication exceptions
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorResponse> handleAuthenticationException(
            AuthenticationException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("Authentication exception occurred. Request ID: {}", requestId);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Authentication Error")
                .message("Authentication failed")
                .errorCode(ErrorCode.SEC_AUTHENTICATION_FAILED)
                .statusCode(HttpStatus.UNAUTHORIZED.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    /**
     * Handle authorization exceptions
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponse> handleAccessDeniedException(
            AccessDeniedException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("Access denied exception occurred. Request ID: {}", requestId);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Authorization Error")
                .message("Access denied")
                .errorCode(ErrorCode.SEC_PERMISSION_DENIED)
                .statusCode(HttpStatus.FORBIDDEN.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * Handle bad credentials exception
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ErrorResponse> handleBadCredentialsException(
            BadCredentialsException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("Bad credentials exception occurred. Request ID: {}", requestId);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Authentication Error")
                .message("Invalid credentials")
                .errorCode(ErrorCode.SEC_AUTHENTICATION_FAILED)
                .statusCode(HttpStatus.UNAUTHORIZED.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    /**
     * Handle resource not found exceptions
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ErrorResponse> handleNoHandlerFoundException(
            NoHandlerFoundException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("No handler found exception occurred. Request ID: {}, Path: {}", 
                requestId, request.getRequestURI());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Not Found")
                .message("The requested resource was not found")
                .errorCode(ErrorCode.BUS_RESOURCE_NOT_FOUND)
                .statusCode(HttpStatus.NOT_FOUND.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "path", request.getRequestURI()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * Handle HTTP method not supported exceptions
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleHttpRequestMethodNotSupportedException(
            HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("HTTP method not supported exception occurred. Request ID: {}, Method: {}, Path: {}", 
                requestId, ex.getMethod(), request.getRequestURI());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Method Not Allowed")
                .message("HTTP method '" + ex.getMethod() + "' is not supported")
                .errorCode(ErrorCode.BUS_INVALID_INPUT)
                .statusCode(HttpStatus.METHOD_NOT_ALLOWED.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "method", ex.getMethod(),
                        "path", request.getRequestURI()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(errorResponse);
    }

    /**
     * Handle HTTP media type not supported exceptions
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleHttpMediaTypeNotSupportedException(
            HttpMediaTypeNotSupportedException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("HTTP media type not supported exception occurred. Request ID: {}, Content-Type: {}", 
                requestId, ex.getContentType());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Unsupported Media Type")
                .message("Content-Type '" + ex.getContentType() + "' is not supported")
                .errorCode(ErrorCode.BUS_INVALID_INPUT)
                .statusCode(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "contentType", ex.getContentType()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(errorResponse);
    }

    /**
     * Handle missing request parameter exceptions
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponse> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("Missing request parameter exception occurred. Request ID: {}, Parameter: {}", 
                requestId, ex.getParameterName());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Bad Request")
                .message("Required parameter '" + ex.getParameterName() + "' is missing")
                .errorCode(ErrorCode.VAL_REQUIRED_FIELD)
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "parameter", ex.getParameterName()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle method argument type mismatch exceptions
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorResponse> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("Method argument type mismatch exception occurred. Request ID: {}, Parameter: {}, Value: {}", 
                requestId, ex.getName(), ex.getValue());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Bad Request")
                .message("Parameter '" + ex.getName() + "' has invalid type")
                .errorCode(ErrorCode.VAL_INVALID_FORMAT)
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "parameter", ex.getName(),
                        "value", ex.getValue(),
                        "requiredType", ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : null
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle HTTP message not readable exceptions
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.warn("HTTP message not readable exception occurred. Request ID: {}", requestId);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Bad Request")
                .message("Request body is invalid or malformed")
                .errorCode(ErrorCode.VAL_INVALID_FORMAT)
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName()
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle all other uncaught exceptions
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, HttpServletRequest request) {
        String requestId = generateRequestId(request);
        
        log.error("Unhandled exception occurred. Request ID: {}, Message: {}", 
                requestId, ex.getMessage(), ex);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .error("Internal Server Error")
                .message("An unexpected error occurred")
                .errorCode(ErrorCode.SYS_INTERNAL_ERROR)
                .statusCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .requestId(requestId)
                .timestamp(LocalDateTime.now())
                .debugInfo(Map.of(
                        "exception", ex.getClass().getSimpleName(),
                        "stackTrace", getStackTraceAsString(ex)
                ))
                .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Generate unique request ID
     */
    private String generateRequestId(HttpServletRequest request) {
        String requestId = request.getHeader(REQUEST_ID_HEADER);
        if (requestId == null || requestId.trim().isEmpty()) {
            requestId = UUID.randomUUID().toString();
        }
        return requestId;
    }

    /**
     * Convert exception stack trace to string
     */
    private String getStackTraceAsString(Exception ex) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        ex.printStackTrace(pw);
        return sw.toString();
    }
}