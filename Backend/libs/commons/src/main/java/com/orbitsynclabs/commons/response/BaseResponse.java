package com.orbitsynclabs.commons.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Base response wrapper for all API responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseResponse<T> {
    
    @JsonProperty("data")
    private T data;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("statusCode")
    private int statusCode;
    
    @JsonProperty("requestId")
    private String requestId;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
    
    /**
     * Create a successful response with data
     */
    public static <T> BaseResponse<T> success(T data) {
        return BaseResponse.<T>builder()
                .data(data)
                .message("Success")
                .statusCode(200)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a successful response with data and custom message
     */
    public static <T> BaseResponse<T> success(T data, String message) {
        return BaseResponse.<T>builder()
                .data(data)
                .message(message)
                .statusCode(200)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a successful response with data, message, and metadata
     */
    public static <T> BaseResponse<T> success(T data, String message, Map<String, Object> metadata) {
        return BaseResponse.<T>builder()
                .data(data)
                .message(message)
                .statusCode(200)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .metadata(metadata)
                .build();
    }
    
    /**
     * Create a successful response without data
     */
    public static <T> BaseResponse<T> success(String message) {
        return BaseResponse.<T>builder()
                .message(message)
                .statusCode(200)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a created response (HTTP 201)
     */
    public static <T> BaseResponse<T> created(T data, String message) {
        return BaseResponse.<T>builder()
                .data(data)
                .message(message)
                .statusCode(201)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create an accepted response (HTTP 202)
     */
    public static <T> BaseResponse<T> accepted(T data, String message) {
        return BaseResponse.<T>builder()
                .data(data)
                .message(message)
                .statusCode(202)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a no content response (HTTP 204)
     */
    public static <T> BaseResponse<T> noContent(String message) {
        return BaseResponse.<T>builder()
                .message(message)
                .statusCode(204)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Generate unique request ID
     */
    private static String generateRequestId() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * Add metadata to existing response
     */
    public BaseResponse<T> withMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
        return this;
    }
    
    /**
     * Add custom metadata key-value pair
     */
    public BaseResponse<T> withMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new java.util.HashMap<>();
        }
        this.metadata.put(key, value);
        return this;
    }
    
    /**
     * Check if response is successful (2xx status codes)
     */
    public boolean isSuccess() {
        return statusCode >= 200 && statusCode < 300;
    }
    
    /**
     * Check if response has client error (4xx status codes)
     */
    public boolean isClientError() {
        return statusCode >= 400 && statusCode < 500;
    }
    
    /**
     * Check if response has server error (5xx status codes)
     */
    public boolean isServerError() {
        return statusCode >= 500;
    }
}