package com.orbitsynclabs.commons.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Represents validation error details in error responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationError {
    
    private String field;
    private String message;
    private String rejectedValue;
    
    /**
     * Create a validation error for a field
     */
    public static ValidationError of(String field, String message) {
        return ValidationError.builder()
                .field(field)
                .message(message)
                .build();
    }
    
    /**
     * Create a validation error for a field with rejected value
     */
    public static ValidationError of(String field, String message, String rejectedValue) {
        return ValidationError.builder()
                .field(field)
                .message(message)
                .rejectedValue(rejectedValue)
                .build();
    }
    
    /**
     * Convert Spring validation errors to ValidationError list
     */
    public static List<ValidationError> fromBindingErrors(org.springframework.validation.BindingResult bindingResult) {
        return bindingResult.getFieldErrors().stream()
                .map(error -> ValidationError.builder()
                        .field(error.getField())
                        .message(error.getDefaultMessage())
                        .rejectedValue(error.getRejectedValue() != null ? error.getRejectedValue().toString() : null)
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * Convert MethodArgumentNotValidException errors to ValidationError list
     */
    public static List<ValidationError> fromMethodArgumentNotValidException(org.springframework.web.bind.MethodArgumentNotValidException ex) {
        return ex.getBindingResult().getFieldErrors().stream()
                .map(error -> ValidationError.builder()
                        .field(error.getField())
                        .message(error.getDefaultMessage())
                        .rejectedValue(error.getRejectedValue() != null ? error.getRejectedValue().toString() : null)
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * Create a validation error for a non-field specific error
     */
    public static ValidationError nonField(String message) {
        return ValidationError.builder()
                .field(null)
                .message(message)
                .build();
    }
    
    /**
     * Create a validation error for a non-field specific error with rejected value
     */
    public static ValidationError nonField(String message, String rejectedValue) {
        return ValidationError.builder()
                .field(null)
                .message(message)
                .rejectedValue(rejectedValue)
                .build();
    }
    
    /**
     * Check if this is a field-specific validation error
     */
    public boolean isFieldError() {
        return field != null && !field.trim().isEmpty();
    }
    
    /**
     * Check if this is a non-field validation error
     */
    public boolean isNonFieldError() {
        return field == null || field.trim().isEmpty();
    }
    
    /**
     * Get the field name with proper formatting
     */
    public String getFormattedField() {
        if (field == null || field.trim().isEmpty()) {
            return "General";
        }
        return field;
    }
    
    /**
     * Get the error message with field context
     */
    public String getFormattedMessage() {
        if (isFieldError()) {
            return String.format("Field '%s': %s", field, message);
        }
        return message;
    }
}