package com.orbitsynclabs.commons.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Error response wrapper for API error responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {
    
    @JsonProperty("error")
    private String error;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("errorCode")
    private String errorCode;
    
    @JsonProperty("statusCode")
    private int statusCode;
    
    @JsonProperty("requestId")
    private String requestId;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("validationErrors")
    private List<ValidationError> validationErrors;
    
    @JsonProperty("debugInfo")
    private Map<String, Object> debugInfo;
    
    /**
     * Create a bad request error response
     */
    public static ErrorResponse badRequest(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Bad Request")
                .message(message)
                .errorCode(errorCode)
                .statusCode(400)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a bad request error response with validation errors
     */
    public static ErrorResponse badRequest(String message, String errorCode, 
                                        List<ValidationError> validationErrors) {
        return ErrorResponse.builder()
                .error("Bad Request")
                .message(message)
                .errorCode(errorCode)
                .statusCode(400)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .validationErrors(validationErrors)
                .build();
    }
    
    /**
     * Create an unauthorized error response
     */
    public static ErrorResponse unauthorized(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Unauthorized")
                .message(message)
                .errorCode(errorCode)
                .statusCode(401)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a forbidden error response
     */
    public static ErrorResponse forbidden(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Forbidden")
                .message(message)
                .errorCode(errorCode)
                .statusCode(403)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a not found error response
     */
    public static ErrorResponse notFound(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Not Found")
                .message(message)
                .errorCode(errorCode)
                .statusCode(404)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a conflict error response
     */
    public static ErrorResponse conflict(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Conflict")
                .message(message)
                .errorCode(errorCode)
                .statusCode(409)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create an unprocessable entity error response
     */
    public static ErrorResponse unprocessableEntity(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Unprocessable Entity")
                .message(message)
                .errorCode(errorCode)
                .statusCode(422)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create an internal server error response
     */
    public static ErrorResponse internalServerError(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Internal Server Error")
                .message(message)
                .errorCode(errorCode)
                .statusCode(500)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a service unavailable error response
     */
    public static ErrorResponse serviceUnavailable(String message, String errorCode) {
        return ErrorResponse.builder()
                .error("Service Unavailable")
                .message(message)
                .errorCode(errorCode)
                .statusCode(503)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a custom error response
     */
    public static ErrorResponse custom(String error, String message, String errorCode, 
                                    int statusCode) {
        return ErrorResponse.builder()
                .error(error)
                .message(message)
                .errorCode(errorCode)
                .statusCode(statusCode)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a custom error response with debug info
     */
    public static ErrorResponse custom(String error, String message, String errorCode, 
                                    int statusCode, Map<String, Object> debugInfo) {
        return ErrorResponse.builder()
                .error(error)
                .message(message)
                .errorCode(errorCode)
                .statusCode(statusCode)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .debugInfo(debugInfo)
                .build();
    }
    
    /**
     * Create a custom error response with validation errors
     */
    public static ErrorResponse custom(String error, String message, String errorCode, 
                                    int statusCode, List<ValidationError> validationErrors) {
        return ErrorResponse.builder()
                .error(error)
                .message(message)
                .errorCode(errorCode)
                .statusCode(statusCode)
                .requestId(generateRequestId())
                .timestamp(LocalDateTime.now())
                .validationErrors(validationErrors)
                .build();
    }
    
    /**
     * Generate unique request ID
     */
    private static String generateRequestId() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * Add debug info to existing error response
     */
    public ErrorResponse withDebugInfo(Map<String, Object> debugInfo) {
        this.debugInfo = debugInfo;
        return this;
    }
    
    /**
     * Add custom debug info key-value pair
     */
    public ErrorResponse withDebugInfo(String key, Object value) {
        if (this.debugInfo == null) {
            this.debugInfo = new java.util.HashMap<>();
        }
        this.debugInfo.put(key, value);
        return this;
    }
    
    /**
     * Add validation errors to existing error response
     */
    public ErrorResponse withValidationErrors(List<ValidationError> validationErrors) {
        this.validationErrors = validationErrors;
        return this;
    }
    
    /**
     * Add a single validation error
     */
    public ErrorResponse withValidationError(ValidationError validationError) {
        if (this.validationErrors == null) {
            this.validationErrors = new java.util.ArrayList<>();
        }
        this.validationErrors.add(validationError);
        return this;
    }
    
    /**
     * Check if response has validation errors
     */
    public boolean hasValidationErrors() {
        return validationErrors != null && !validationErrors.isEmpty();
    }
    
    /**
     * Check if response has debug info
     */
    public boolean hasDebugInfo() {
        return debugInfo != null && !debugInfo.isEmpty();
    }
}