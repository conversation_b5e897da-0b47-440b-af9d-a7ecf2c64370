package com.orbitsynclabs.commons.exception;

/**
 * System exception for technical/runtime errors
 */
public class SystemException extends RuntimeException {
    
    private final String errorCode;
    private final String technicalDetails;
    
    public SystemException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.technicalDetails = null;
    }
    
    public SystemException(String errorCode, String message, String technicalDetails) {
        super(message);
        this.errorCode = errorCode;
        this.technicalDetails = technicalDetails;
    }
    
    public SystemException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.technicalDetails = null;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getTechnicalDetails() {
        return technicalDetails;
    }
}