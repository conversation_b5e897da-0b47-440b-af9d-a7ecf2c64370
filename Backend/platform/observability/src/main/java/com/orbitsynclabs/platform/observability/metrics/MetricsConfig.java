package com.orbitsynclabs.platform.observability.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.config.MeterFilterReply;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.springframework.boot.actuator.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Metrics and Monitoring
 * Provides Prometheus integration with custom metrics
 */
@Configuration
public class MetricsConfig {

    /**
     * Configure Meter Registry with common tags
     */
    @Bean
    MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags(
            "application", "orbitsynclabs-refermitra",
            "version", "1.0.0",
            "environment", "${ENVIRONMENT:local}"
        );
    }

    /**
     * Configure Prometheus Meter Registry
     */
    @Bean
    PrometheusMeterRegistry prometheusMeterRegistry() {
        PrometheusMeterRegistry registry = new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
        
        // Add custom filters
        registry.config().meterFilter(MeterFilter.denyNameStartsWith("jvm"));
        registry.config().meterFilter(MeterFilter.denyNameStartsWith("system"));
        registry.config().meterFilter(MeterFilter.denyNameStartsWith("process"));
        
        // Add acceptance filter for application metrics
        registry.config().meterFilter(new MeterFilter() {
            @Override
            public MeterFilterReply accept(Meter.Id id) {
                return id.getName().startsWith("orbitsynclabs") ? 
                    MeterFilterReply.ACCEPT : MeterFilterReply.DENY;
            }
        });
        
        return registry;
    }

    /**
     * Custom metrics for business operations
     */
    @Bean
    BusinessMetrics businessMetrics(MeterRegistry meterRegistry) {
        return new BusinessMetrics(meterRegistry);
    }
}
