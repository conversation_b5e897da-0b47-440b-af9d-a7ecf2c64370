package com.orbitsynclabs.platform.observability.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Custom business metrics for application monitoring
 */
@Component
public class BusinessMetrics {

    private final MeterRegistry meterRegistry;
    
    // Counters for business operations
    private final Counter requestCounter;
    private final Counter errorCounter;
    private final Counter successCounter;
    
    // Gauges for system health
    private final AtomicInteger activeConnections;
    private final AtomicInteger pendingRequests;
    private final AtomicLong totalProcessingTime;
    
    // Timers for performance monitoring
    private final Timer apiCallTimer;
    private final Timer databaseQueryTimer;
    private final Timer externalServiceTimer;

    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize counters
        this.requestCounter = Counter.builder("orbitsynclabs.requests.total")
            .description("Total number of requests")
            .register(meterRegistry);
            
        this.errorCounter = Counter.builder("orbitsynclabs.errors.total")
            .description("Total number of errors")
            .register(meterRegistry);
            
        this.successCounter = Counter.builder("orbitsynclabs.success.total")
            .description("Total number of successful operations")
            .register(meterRegistry);
        
        // Initialize gauges
        this.activeConnections = new AtomicInteger(0);
        this.pendingRequests = new AtomicInteger(0);
        this.totalProcessingTime = new AtomicLong(0);
        
        Gauge.builder("orbitsynclabs.connections.active")
            .description("Active database connections")
            .register(meterRegistry, activeConnections, AtomicInteger::get);
            
        Gauge.builder("orbitsynclabs.requests.pending")
            .description("Pending requests in queue")
            .register(meterRegistry, pendingRequests, AtomicInteger::get);
            
        Gauge.builder("orbitsynclabs.processing.time.total")
            .description("Total processing time in nanoseconds")
            .register(meterRegistry, totalProcessingTime, AtomicLong::get);
        
        // Initialize timers
        this.apiCallTimer = Timer.builder("orbitsynclabs.api.calls")
            .description("API call execution time")
            .register(meterRegistry);
            
        this.databaseQueryTimer = Timer.builder("orbitsynclabs.database.queries")
            .description("Database query execution time")
            .register(meterRegistry);
            
        this.externalServiceTimer = Timer.builder("orbitsynclabs.external.services")
            .description("External service call execution time")
            .register(meterRegistry);
    }

    // Counter methods
    public void incrementRequestCounter() {
        requestCounter.increment();
    }

    public void incrementErrorCounter() {
        errorCounter.increment();
    }

    public void incrementSuccessCounter() {
        successCounter.increment();
    }

    // Gauge methods
    public void setActiveConnections(int count) {
        activeConnections.set(count);
    }

    public void incrementPendingRequests() {
        pendingRequests.increment();
    }

    public void decrementPendingRequests() {
        pendingRequests.decrementAndGet();
    }

    public void addProcessingTime(long nanos) {
        totalProcessingTime.addAndGet(nanos);
    }

    // Timer methods
    public Timer.Sample startApiCallTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordApiCallTime(Timer.Sample sample) {
        sample.stop(apiCallTimer);
    }

    public Timer.Sample startDatabaseQueryTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordDatabaseQueryTime(Timer.Sample sample) {
        sample.stop(databaseQueryTimer);
    }

    public Timer.Sample startExternalServiceTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordExternalServiceTime(Timer.Sample sample) {
        sample.stop(externalServiceTimer);
    }

    // Custom business metrics
    public void recordUserLogin(String userType) {
        Counter.builder("orbitsynclabs.user.logins")
            .tag("userType", userType)
            .description("User login attempts")
            .register(meterRegistry)
            .increment();
    }

    public void recordApiUsage(String endpoint, String method) {
        Counter.builder("orbitsynclabs.api.usage")
            .tag("endpoint", endpoint)
            .tag("method", method)
            .description("API usage by endpoint and method")
            .register(meterRegistry)
            .increment();
    }

    public void recordBusinessEvent(String eventType, String result) {
        Counter.builder("orbitsynclabs.business.events")
            .tag("eventType", eventType)
            .tag("result", result)
            .description("Business events tracking")
            .register(meterRegistry)
            .increment();
    }
}
