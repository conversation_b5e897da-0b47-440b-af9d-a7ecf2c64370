package com.orbitsynclabs.platform.observability.health;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import org.springframework.stereotype.Component;

/**
 * Circuit breaker metrics for monitoring circuit breaker health
 */
@Component
public class CircuitBreakerMetrics implements MeterBinder {

    private volatile int circuitBreakerState = 0; // 0 = closed, 1 = open, 2 = half-open
    private volatile int failureCount = 0;
    private volatile int successCount = 0;
    private volatile int totalCalls = 0;

    @Override
    public void bindTo(MeterRegistry meterRegistry) {
        Gauge.builder("circuit.breaker.state")
            .description("Current circuit breaker state (0=closed, 1=open, 2=half-open)")
            .register(meterRegistry, this, CircuitBreakerMetrics::getCircuitBreakerState);

        Gauge.builder("circuit.breaker.failures")
            .description("Number of failures in current window")
            .register(meterRegistry, this, CircuitBreakerMetrics::getFailureCount);

        Gauge.builder("circuit.breaker.successes")
            .description("Number of successes in current window")
            .register(meterRegistry, this, CircuitBreakerMetrics::getSuccessCount);

        Gauge.builder("circuit.breaker.total.calls")
            .description("Total number of calls in current window")
            .register(meterRegistry, this, CircuitBreakerMetrics::getTotalCalls);

        Gauge.builder("circuit.breaker.failure.rate")
            .description("Failure rate as percentage")
            .register(meterRegistry, this, CircuitBreakerMetrics::getFailureRate);
    }

    public void recordSuccess() {
        successCount++;
        totalCalls++;
    }

    public void recordFailure() {
        failureCount++;
        totalCalls++;
    }

    public void setState(int state) {
        this.circuitBreakerState = state;
    }

    public void reset() {
        failureCount = 0;
        successCount = 0;
        totalCalls = 0;
    }

    // Getter methods for gauges
    public double getCircuitBreakerState() {
        return circuitBreakerState;
    }

    public double getFailureCount() {
        return failureCount;
    }

    public double getSuccessCount() {
        return successCount;
    }

    public double getTotalCalls() {
        return totalCalls;
    }

    public double getFailureRate() {
        return totalCalls == 0 ? 0 : (failureCount * 100.0) / totalCalls;
    }
}
