<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bc5a4aff-dec6-44ec-accc-20469b68572c" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Docs/Tasks.md" beforeDir="false" afterPath="$PROJECT_DIR$/../Docs/Tasks.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2wL2a9QxDaB2RIi3P3mAY6Hzo9Y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.TestExecutionServiceApplication.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Remote JVM Debug.Unnamed.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "Develop",
    "last_opened_file_path": "/home/<USER>/Documents/MYFOLDER/refermitra/Backend",
    "settings.editor.selected.configurable": "Errors"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Unnamed" type="Remote" nameIsGenerated="true">
      <module name="test-execution-service" />
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="localhost" />
      <option name="PORT" value="5005" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="5005" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bc5a4aff-dec6-44ec-accc-20469b68572c" name="Changes" comment="" />
      <created>1745806593483</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745806593483</updated>
    </task>
    <servers />
  </component>
</project>