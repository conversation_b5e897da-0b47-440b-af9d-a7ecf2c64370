import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/context/AuthContext";
import { DataProvider } from "@/context/DataContext";

// Layouts
import MainLayout from "@/components/layouts/MainLayout";

// Home and auth pages
import Index from "@/pages/Home/Index";
import Login from "@/pages/Auth/Login";
import Register from "@/pages/Auth/Register";
import Pricing from "@/components/pages/Pricing";

// Provider pages
import ProviderDashboard from "@/pages/Provider/Dashboard";

// Candidate pages
import CandidateDashboard from "@/pages/Candidate/Dashboard";
import JobSearch from "@/pages/Candidate/JobSearch";
import JobDetail from "@/pages/Candidate/JobDetail";

// Admin pages
import AdminDashboard from "@/pages/Admin/Dashboard";
import CreateTest from "@/pages/Admin/CreateTest";
import Tests from "@/pages/Admin/Tests";

// General pages
import NotFound from "@/pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <BrowserRouter>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <DataProvider>
          <TooltipProvider>
            <Sonner />
            <Routes>
              <Route element={<MainLayout />}>
                {/* Home and Auth Routes */}
                <Route path="/" element={<Index />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/pricing" element={<Pricing />} />
                
                {/* Provider Routes */}
                <Route path="/provider" element={<ProviderDashboard />} />
                <Route path="/provider/jobs" element={<ProviderDashboard />} />
                <Route path="/provider/referrals" element={<ProviderDashboard />} />
                
                {/* Candidate Routes */}
                <Route path="/candidate" element={<CandidateDashboard />} />
                <Route path="/candidate/tests" element={<CandidateDashboard />} />
                <Route path="/candidate/referrals" element={<CandidateDashboard />} />
                <Route path="/candidate/jobs" element={<JobSearch />} />
                <Route path="/candidate/jobs/:id" element={<JobDetail />} />
                
                {/* Admin Routes */}
                <Route path="/admin" element={<AdminDashboard />} />
                <Route path="/admin/tests" element={<Tests />} />
                <Route path="/admin/tests/create" element={<CreateTest />} />
                <Route path="/admin/users" element={<AdminDashboard />} />
                
                {/* Catch-all route */}
                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
            <Toaster />
          </TooltipProvider>
        </DataProvider>
      </AuthProvider>
    </QueryClientProvider>
  </BrowserRouter>
);

export default App;
