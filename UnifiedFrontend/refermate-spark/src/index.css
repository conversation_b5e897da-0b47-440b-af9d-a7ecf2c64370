
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 45%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', sans-serif;
  }

  /* Apple-inspired scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-black/10 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-black/20;
  }
}

@layer components {
  .glass {
    @apply bg-white/80 backdrop-blur-lg border border-white/20 shadow-glass;
  }
  
  .glass-dark {
    @apply bg-black/10 backdrop-blur-lg border border-white/10 shadow-glass; 
  }
  
  .glass-hover {
    @apply hover:bg-white/90 transition-all duration-300 ease-apple;
  }
  
  .text-balance {
    text-wrap: balance;
  }
  
  .page-transition-container {
    @apply min-h-screen w-full;
  }
  
  .layout-grid {
    @apply grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6;
  }
  
  .btn-transition {
    @apply transition-all duration-300 ease-apple;
  }
  
  .depth-effect {
    box-shadow: 0 1px 1px rgba(0,0,0,0.01), 
                0 2px 2px rgba(0,0,0,0.01), 
                0 4px 4px rgba(0,0,0,0.01), 
                0 8px 8px rgba(0,0,0,0.01);
  }
  
  .premium-card {
    @apply rounded-2xl bg-white border border-black/5 p-6 shadow-premium;
  }
}

/* Page transition animations */
.page-enter {
  opacity: 0;
  transform: translateY(8px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-8px);
  transition: opacity 250ms, transform 250ms;
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}
