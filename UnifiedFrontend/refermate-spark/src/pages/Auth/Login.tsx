
import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { ArrowRight, Loader2 } from "lucide-react";

const Login: React.FC = () => {
  const { login, loginWithGoogle, loginWithLinkedIn, isLoading } = useAuth();
  const navigate = useNavigate();
  
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [formError, setFormError] = useState("");
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError("");
    
    if (!email.trim() || !password.trim()) {
      setFormError("Please enter both email and password");
      return;
    }
    
    try {
      await login(email, password);
    } catch (error) {
      console.error("Login error:", error);
      setFormError("An error occurred during login");
    }
  };
  
  const handleGoogleLogin = async () => {
    try {
      await loginWithGoogle();
    } catch (error) {
      console.error("Google login error:", error);
      toast.error("An error occurred during Google login");
    }
  };
  
  const handleLinkedInLogin = async () => {
    try {
      await loginWithLinkedIn();
    } catch (error) {
      console.error("LinkedIn login error:", error);
      toast.error("An error occurred during LinkedIn login");
    }
  };
  
  // Helpful login info for demo
  const loginAsCandidateDemo = () => {
    setEmail("<EMAIL>");
    setPassword("password");
  };
  
  const loginAsProviderDemo = () => {
    setEmail("<EMAIL>");
    setPassword("password");
  };
  
  const loginAsAdminDemo = () => {
    setEmail("<EMAIL>");
    setPassword("password");
  };

  return (
    <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
      <div className="w-full max-w-md">
        <div className="text-center mb-8 animate-in fade-in duration-1000">
          <h1 className="text-3xl font-bold">Welcome back</h1>
          <p className="text-muted-foreground mt-2">
            Log in to your Refermitra account
          </p>
        </div>
        
        <div className="premium-card animate-in fade-in scale-in-center duration-1000">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  to="/forgot-password"
                  className="text-xs text-refermitra-500 hover:text-refermitra-600 hover:underline transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            
            {formError && (
              <div className="text-sm text-destructive">{formError}</div>
            )}
            
            <Button
              type="submit"
              className="w-full bg-refermitra-500 hover:bg-refermitra-600"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                "Log in"
              )}
            </Button>
          </form>
          
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border"></div>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-card px-2 text-muted-foreground">Or continue with</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleGoogleLogin}
              disabled={isLoading}
              className="w-full"
            >
              <svg
                className="mr-2 h-4 w-4"
                aria-hidden="true"
                focusable="false"
                data-prefix="fab"
                data-icon="google"
                role="img"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 488 512"
              >
                <path
                  fill="currentColor"
                  d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"
                ></path>
              </svg>
              Google
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleLinkedInLogin}
              disabled={isLoading}
              className="w-full"
            >
              <svg
                className="mr-2 h-4 w-4"
                aria-hidden="true"
                focusable="false"
                data-prefix="fab"
                data-icon="linkedin"
                role="img"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 448 512"
              >
                <path
                  fill="currentColor"
                  d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"
                ></path>
              </svg>
              LinkedIn
            </Button>
          </div>
          
          <div className="mt-6 text-center text-sm">
            <p className="text-muted-foreground">
              Don't have an account?{" "}
              <Link
                to="/register"
                className="font-medium text-refermitra-500 hover:text-refermitra-600 hover:underline transition-colors"
              >
                Sign up
              </Link>
            </p>
          </div>
        </div>
        
        {/* Demo accounts info box */}
        <div className="mt-8 p-4 glass rounded-xl animate-in fade-in duration-1000 delay-300">
          <h3 className="text-sm font-medium mb-2">Demo Accounts</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={loginAsProviderDemo}
              className="text-xs py-1 h-auto"
            >
              Use Provider
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loginAsCandidateDemo}
              className="text-xs py-1 h-auto"
            >
              Use Candidate
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loginAsAdminDemo}
              className="text-xs py-1 h-auto"
            >
              Use Admin
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Click any button to pre-fill login credentials, then click "Log in"
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
