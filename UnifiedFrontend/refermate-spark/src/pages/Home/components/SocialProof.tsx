
import React from "react";

interface CompanyLogo {
  name: string;
  logo: string;
  width: number;
}

const companies: CompanyLogo[] = [
  {
    name: "Google",
    logo: "/src/assets/images/companies/google.svg",
    width: 100
  },
  {
    name: "Microsoft",
    logo: "/src/assets/images/companies/microsoft.svg",
    width: 110
  },
  {
    name: "Amazon",
    logo: "/src/assets/images/companies/amazon.svg",
    width: 100
  },
  {
    name: "Apple",
    logo: "/src/assets/images/companies/apple.svg",
    width: 30
  },
  {
    name: "<PERSON><PERSON>",
    logo: "/src/assets/images/companies/meta.svg",
    width: 100
  }
];

const SocialProof: React.FC = () => {
  return (
    <section className="py-16 bg-gradient-to-b from-transparent to-refermitra-50/50">
      <div className="container max-w-7xl mx-auto px-4">
        <div className="text-center animate-in fade-in duration-1000 delay-500">
          <p className="text-lg font-medium text-refermitra-500 mb-8">Trusted by leading companies</p>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16">
            {companies.map((company) => (
              <div 
                key={company.name} 
                className="opacity-60 hover:opacity-100 transition-opacity flex items-center"
                title={company.name}
              >
                <img 
                  src={company.logo} 
                  alt={`${company.name} logo`} 
                  className="h-8 w-auto dark:invert"
                  style={{ width: company.width }}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialProof;
