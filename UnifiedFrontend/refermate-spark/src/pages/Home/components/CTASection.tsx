
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useAuth } from "@/context/AuthContext";

const CTASection: React.FC = () => {
  const { user } = useAuth();
  
  return (
    <section className="py-24 px-4 relative overflow-hidden">
      <div className="absolute inset-0 bg-refermitra-500/5 z-0"></div>
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-refermitra-100 rounded-full filter blur-3xl opacity-50"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full filter blur-3xl opacity-50"></div>
      
      <div className="container max-w-5xl mx-auto relative z-10">
        <div className="premium-card text-center animate-in fade-in scale-in-center duration-1000 delay-1000">
          <h2 className="text-3xl sm:text-4xl font-bold">Ready to transform your career?</h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            Join Refermitra today and connect with industry professionals who can refer you to your dream job.
          </p>
          <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-refermitra-500 hover:bg-refermitra-600">
              <Link to={user ? (user.role === "provider" ? "/provider" : "/candidate") : "/register"}>
                {user ? "Go to Dashboard" : "Get Started for Free"}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/">
                View Pricing
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
