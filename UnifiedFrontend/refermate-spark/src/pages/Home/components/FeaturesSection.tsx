
import React, { useRef, useEffect } from "react";
import { Users, Code, Award } from "lucide-react";

const FeatureCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: string;
}> = ({ icon, title, description, delay }) => {
  return (
    <div 
      className="feature-card flex flex-col items-center p-8 rounded-2xl border border-border bg-card shadow-soft hover:shadow-premium transition-all duration-500 animate-in fade-in"
    >
      <div className="w-12 h-12 rounded-full bg-refermitra-100 flex items-center justify-center mb-6">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-3">{title}</h3>
      <p className="text-center text-muted-foreground">
        {description}
      </p>
    </div>
  );
};

const FeaturesSection: React.FC = () => {
  const featuresRef = useRef<HTMLDivElement>(null);
  
  // Animate features on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in");
          }
        });
      },
      { threshold: 0.1 }
    );
    
    const features = document.querySelectorAll(".feature-card");
    features.forEach((feature) => {
      observer.observe(feature);
    });
    
    return () => {
      features.forEach((feature) => {
        observer.unobserve(feature);
      });
    };
  }, []);

  return (
    <section 
      ref={featuresRef}
      className="py-24 px-4"
    >
      <div className="container max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-in fade-in duration-1000 delay-500">
          <h2 className="text-3xl sm:text-4xl font-bold">How It Works</h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            Our platform streamlines the referral process with skill validation, ensuring only qualified candidates get referred.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <FeatureCard 
            icon={<Users className="h-6 w-6 text-refermitra-700" />}
            title="Connect"
            description="Candidates connect with referral providers who have access to job openings at their companies."
          />
          
          <FeatureCard 
            icon={<Code className="h-6 w-6 text-refermitra-700" />}
            title="Test"
            description="Take skill-based assessments to validate your qualifications for the positions you want."
            delay="0.2s"
          />
          
          <FeatureCard 
            icon={<Award className="h-6 w-6 text-refermitra-700" />}
            title="Get Referred"
            description="Successfully pass the assessment and get referred by professionals within your target company."
            delay="0.4s"
          />
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
