
import React, { useRef, useEffect } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext";
import { ArrowRight, CheckCircle, ChevronRight } from "lucide-react";

const HeroSection: React.FC = () => {
  const { user } = useAuth();
  const heroRef = useRef<HTMLDivElement>(null);

  // Simple parallax effect on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (heroRef.current) {
        const scrollPosition = window.scrollY;
        heroRef.current.style.transform = `translateY(${scrollPosition * 0.1}px)`;
        heroRef.current.style.opacity = `${1 - scrollPosition * 0.002}`;
      }
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <section className="pt-32 pb-20 px-4 overflow-hidden relative">
      <div className="absolute inset-0 bg-gradient-to-b from-refermitra-50/50 to-transparent z-0"></div>
      <div 
        ref={heroRef}
        className="container max-w-7xl mx-auto relative z-10"
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="text-center lg:text-left animate-in fade-in duration-1000 delay-200">
            <div className="inline-block bg-refermitra-100 text-refermitra-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
              Revolutionizing Job Referrals
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight text-balance">
              Connect, Test, and{" "}
              <span className="text-refermitra-500">Get Referred</span>
            </h1>
            <p className="mt-6 text-lg sm:text-xl text-muted-foreground max-w-xl mx-auto lg:mx-0">
              Refermitra connects job seekers with referral providers through automated skill assessments, ensuring high-quality matches.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row justify-center lg:justify-start gap-4">
              <Button asChild size="lg" className="bg-refermitra-500 hover:bg-refermitra-600">
                <Link to={user ? (user.role === "provider" ? "/provider" : "/candidate") : "/register"}>
                  {user ? "Go to Dashboard" : "Get Started"}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <Link to="/">
                  Learn more
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="mt-8 flex flex-wrap justify-center lg:justify-start gap-6">
              <div className="flex items-center">
                <CheckCircle className="text-green-500 h-5 w-5 mr-2" />
                <span className="text-sm">Automated Testing</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 h-5 w-5 mr-2" />
                <span className="text-sm">Quality Referrals</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 h-5 w-5 mr-2" />
                <span className="text-sm">Streamlined Process</span>
              </div>
            </div>
          </div>
          <div className="hidden lg:block animate-in fade-in duration-1000 delay-300">
            <div className="relative">
              <div className="absolute -top-6 -right-6 w-64 h-64 bg-refermitra-100 rounded-full filter blur-3xl opacity-50 animate-pulse-subtle"></div>
              <div className="absolute -bottom-8 -left-8 w-64 h-64 bg-blue-100 rounded-full filter blur-3xl opacity-50 animate-pulse-subtle" style={{ animationDelay: "1s" }}></div>
              <img 
                src="/src/assets/images/hero-platform.svg" 
                alt="Refermitra Platform" 
                className="relative rounded-2xl border border-border bg-white shadow-premium z-10 w-full max-w-2xl mx-auto"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
