
import React from "react";
import { Star } from "lucide-react";

interface TestimonialProps {
  name: string;
  role: string;
  image: string;
  quote: string;
  index: number;
}

const Testimonial: React.FC<TestimonialProps> = ({ name, role, image, quote, index }) => {
  return (
    <div
      className="feature-card p-8 rounded-2xl bg-white border border-border shadow-soft animate-in fade-in duration-1000"
    >
      <div className="flex items-center mb-6">
        <img
          src={image}
          alt={name}
          className="w-12 h-12 rounded-full mr-4"
        />
        <div>
          <h4 className="font-semibold">{name}</h4>
          <p className="text-sm text-muted-foreground">{role}</p>
        </div>
      </div>
      <div className="flex mb-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className="h-5 w-5 text-yellow-400 fill-yellow-400"
          />
        ))}
      </div>
      <p className="text-muted-foreground">"{quote}"</p>
    </div>
  );
};

const TestimonialsSection: React.FC = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Software Engineer at Google",
      image: "https://i.pravatar.cc/150?img=5",
      quote: "The skill assessments gave me confidence and my referrer the assurance to recommend me. I got the job within weeks!",
    },
    {
      name: "David Chen",
      role: "Product Manager at Microsoft",
      image: "https://i.pravatar.cc/150?img=11",
      quote: "As a referrer, I love that I can validate candidates' skills before putting my name behind them. It's a win-win.",
    },
    {
      name: "Jennifer Smith",
      role: "Data Scientist at Amazon",
      image: "https://i.pravatar.cc/150?img=9",
      quote: "The platform made it easy to showcase my technical abilities and connect with the right people inside my dream company.",
    },
  ];

  return (
    <section className="py-24 px-4 bg-refermitra-50">
      <div className="container max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-in fade-in duration-1000 delay-700">
          <h2 className="text-3xl sm:text-4xl font-bold">Success Stories</h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            Hear from users who have successfully found their dream jobs through Refermitra.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Testimonial
              key={index}
              {...testimonial}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
