import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Search, 
  MapPin, 
  Briefcase, 
  DollarSign, 
  Building2, 
  Bookmark, 
  BookmarkCheck,
  Calendar,
  Filter,
  ChevronLeft,
  ChevronRight,
  IndianRupee
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useData } from "@/context/DataContext";

// Mock job data - replace with actual API calls
const mockJobs = [
  {
    id: 1,
    title: "Senior Software Engineer",
    company: "Tech Corp",
    location: "Bangalore",
    experience: "5-8 years",
    salary: "25-35 LPA",
    description: "Looking for a Senior Software Engineer with expertise in React and Node.js...",
    skills: ["React", "Node.js", "TypeScript", "AWS"],
    postedDate: "2 days ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology"
  },
  {
    id: 2,
    title: "Frontend Developer",
    company: "Digital Solutions Inc.",
    location: "Mumbai",
    experience: "2-5 years",
    salary: "15-25 LPA",
    description: "Frontend Developer with experience in modern JavaScript frameworks...",
    skills: ["React", "JavaScript", "CSS", "HTML"],
    postedDate: "1 day ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology"
  },
  {
    id: 3,
    title: "Backend Developer",
    company: "Cloud Systems",
    location: "Hyderabad",
    experience: "3-6 years",
    salary: "18-28 LPA",
    description: "Backend Developer with strong knowledge of server-side technologies...",
    skills: ["Node.js", "Python", "MongoDB", "AWS"],
    postedDate: "3 days ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology"
  },
  {
    id: 4,
    title: "DevOps Engineer",
    company: "Tech Innovations",
    location: "Pune",
    experience: "4-7 years",
    salary: "22-32 LPA",
    description: "DevOps Engineer with experience in CI/CD pipelines and cloud infrastructure...",
    skills: ["Docker", "Kubernetes", "Jenkins", "AWS"],
    postedDate: "1 week ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology"
  },
  {
    id: 5,
    title: "Data Scientist",
    company: "AI Solutions",
    location: "Bangalore",
    experience: "3-6 years",
    salary: "20-30 LPA",
    description: "Data Scientist with expertise in machine learning and data analysis...",
    skills: ["Python", "Machine Learning", "SQL", "TensorFlow"],
    postedDate: "4 days ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology"
  },
  {
    id: 6,
    title: "UI/UX Designer",
    company: "Creative Design Studio",
    location: "Delhi",
    experience: "2-5 years",
    salary: "12-22 LPA",
    description: "UI/UX Designer with a strong portfolio and experience in user-centered design...",
    skills: ["Figma", "Adobe XD", "UI Design", "User Research"],
    postedDate: "2 days ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Design"
  },
  {
    id: 7,
    title: "Product Manager",
    company: "Product Hub",
    location: "Bangalore",
    experience: "5-8 years",
    salary: "25-35 LPA",
    description: "Product Manager with experience in agile methodologies and product strategy...",
    skills: ["Product Strategy", "Agile", "User Stories", "Data Analysis"],
    postedDate: "1 day ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Product"
  },
  {
    id: 8,
    title: "QA Engineer",
    company: "Quality First",
    location: "Chennai",
    experience: "2-5 years",
    salary: "10-20 LPA",
    description: "QA Engineer with experience in automated testing and quality assurance...",
    skills: ["Selenium", "JUnit", "TestNG", "API Testing"],
    postedDate: "5 days ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology"
  }
];

// Mock industries for filter
const industries = [
  "Technology",
  "Finance",
  "Healthcare",
  "Education",
  "Manufacturing",
  "Retail",
  "Design",
  "Product",
  "Marketing",
  "Sales"
];

// Mock job types for filter
const jobTypes = [
  "Full-time",
  "Part-time",
  "Contract",
  "Freelance",
  "Internship"
];

const JobSearch: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [location, setLocation] = useState("");
  const [experience, setExperience] = useState("");
  const [salary, setSalary] = useState([0, 50]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([]);
  const [savedJobs, setSavedJobs] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const jobsPerPage = 5;

  // Filter jobs based on search criteria
  const filteredJobs = mockJobs.filter(job => {
    const matchesSearch = searchQuery === "" || 
      job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesLocation = location === "" || 
      job.location.toLowerCase().includes(location.toLowerCase());
    
    const matchesExperience = experience === "" || 
      job.experience === experience;
    
    const matchesSalary = job.salary.split("-").map(s => parseInt(s.replace(" LPA", "")))[0] >= salary[0] &&
      job.salary.split("-").map(s => parseInt(s.replace(" LPA", "")))[1] <= salary[1];
    
    const matchesIndustry = selectedIndustries.length === 0 || 
      selectedIndustries.includes(job.industry);
    
    const matchesJobType = selectedJobTypes.length === 0 || 
      selectedJobTypes.includes(job.jobType);
    
    return matchesSearch && matchesLocation && matchesExperience && 
           matchesSalary && matchesIndustry && matchesJobType;
  });

  // Pagination
  const indexOfLastJob = currentPage * jobsPerPage;
  const indexOfFirstJob = indexOfLastJob - jobsPerPage;
  const currentJobs = filteredJobs.slice(indexOfFirstJob, indexOfLastJob);
  const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);

  const handleSaveJob = (jobId: number) => {
    setSavedJobs(prev => 
      prev.includes(jobId) 
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    );
  };

  const handleIndustryChange = (industry: string) => {
    setSelectedIndustries(prev => 
      prev.includes(industry)
        ? prev.filter(i => i !== industry)
        : [...prev, industry]
    );
  };

  const handleJobTypeChange = (jobType: string) => {
    setSelectedJobTypes(prev => 
      prev.includes(jobType)
        ? prev.filter(t => t !== jobType)
        : [...prev, jobType]
    );
  };

  const handleViewJob = (jobId: number) => {
    navigate(`/candidate/jobs/${jobId}`);
  };

  return (
    <div className="min-h-screen bg-background pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Search Section */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Job Referral Test title, keywords, or company"
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Location"
                  className="pl-10"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                />
              </div>
              <Select value={experience} onValueChange={setExperience}>
                <SelectTrigger>
                  <SelectValue placeholder="Experience" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-2">0-2 years</SelectItem>
                  <SelectItem value="2-5">2-5 years</SelectItem>
                  <SelectItem value="5-8">5-8 years</SelectItem>
                  <SelectItem value="8+">8+ years</SelectItem>
                </SelectContent>
              </Select>
              <Button className="bg-refermitra-500 hover:bg-refermitra-600">
                Search Job Referral Tests
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters */}
          <Card className="h-fit">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold">Filters</CardTitle>
                <Filter className="h-4 w-4" />
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Salary Range */}
              <div>
                <h3 className="font-medium mb-4">Salary Range (LPA)</h3>
                <div className="space-y-4">
                  <Slider
                    value={salary}
                    onValueChange={setSalary}
                    max={50}
                    step={1}
                  />
                  <div className="flex justify-between text-sm">
                    <span>{salary[0]} LPA</span>
                    <span>{salary[1]} LPA</span>
                  </div>
                </div>
              </div>

              {/* Industry */}
              <div>
                <h3 className="font-medium mb-4">Industry</h3>
                <div className="space-y-2">
                  {industries.map((industry) => (
                    <div key={industry} className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedIndustries.includes(industry)}
                        onCheckedChange={() => handleIndustryChange(industry)}
                      />
                      <label className="text-sm">{industry}</label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Job Type */}
              <div>
                <h3 className="font-medium mb-4">Job Referral Test Type</h3>
                <div className="space-y-2">
                  {jobTypes.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedJobTypes.includes(type)}
                        onCheckedChange={() => handleJobTypeChange(type)}
                      />
                      <label className="text-sm">{type}</label>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Job List */}
          <div className="lg:col-span-3">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">
                {filteredJobs.length} Job Referral Tests Found
              </h2>
              <Select defaultValue="recent">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Most Recent</SelectItem>
                  <SelectItem value="salary">Highest Salary</SelectItem>
                  <SelectItem value="relevance">Most Relevant</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {currentJobs.map((job) => (
              <Card key={job.id} className="mb-4">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        {job.title}
                      </CardTitle>
                      <CardDescription className="flex items-center mt-1">
                        <Building2 className="h-4 w-4 mr-1" />
                        {job.company}
                      </CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleSaveJob(job.id)}
                    >
                      {savedJobs.includes(job.id) ? (
                        <BookmarkCheck className="h-5 w-5 text-refermitra-500" />
                      ) : (
                        <Bookmark className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span className="text-sm">{job.location}</span>
                    </div>
                    <div className="flex items-center">
                      <Briefcase className="h-4 w-4 mr-2" />
                      <span className="text-sm">{job.experience}</span>
                    </div>
                    <div className="flex items-center">
                      <IndianRupee className="h-4 w-4 mr-2" />
                      <span className="text-sm">{job.salary}</span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    {job.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {job.skills.map((skill) => (
                      <Badge key={skill} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span className="text-sm text-muted-foreground">
                      Posted {job.postedDate}
                    </span>
                  </div>
                  <Button
                    className="bg-refermitra-500 hover:bg-refermitra-600"
                    onClick={() => handleViewJob(job.id)}
                  >
                    View Job Referral Test
                  </Button>
                </CardFooter>
              </Card>
            ))}

            {/* Pagination */}
            <div className="flex justify-center items-center space-x-4 mt-8">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobSearch; 