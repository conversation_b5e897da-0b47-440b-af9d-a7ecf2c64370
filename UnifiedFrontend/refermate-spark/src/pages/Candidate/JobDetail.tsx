import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { 
  MapPin, 
  Briefcase, 
  Building2, 
  Bookmark, 
  BookmarkCheck, 
  Calendar, 
  Clock, 
  ArrowLeft,
  Share2,
  CheckCircle,
  IndianRupee
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";

// Mock job data - replace with API call
const getJobById = (id: string) => {
  return {
    id: parseInt(id),
    title: "Senior Software Engineer",
    company: "Tech Corp",
    location: "Bangalore",
    experience: "5-8 years",
    salary: "25-35 LPA",
    description: "Looking for a Senior Software Engineer with expertise in React and Node.js...",
    skills: ["React", "Node.js", "TypeScript", "AWS", "Docker", "Kubernetes"],
    postedDate: "2 days ago",
    isSaved: false,
    jobType: "Full-time",
    industry: "Technology",
    companySize: "500-1000 employees",
    companyDescription: "Tech Corp is a leading technology company focused on building innovative solutions...",
    responsibilities: [
      "Design and implement scalable web applications",
      "Collaborate with cross-functional teams to define, design, and ship new features",
      "Write clean, maintainable, and efficient code",
      "Mentor junior developers and conduct code reviews",
      "Stay up-to-date with the latest technologies and best practices"
    ],
    requirements: [
      "5+ years of experience in software development",
      "Strong proficiency in React, Node.js, and TypeScript",
      "Experience with cloud platforms (AWS, GCP, or Azure)",
      "Knowledge of CI/CD pipelines and DevOps practices",
      "Excellent problem-solving and communication skills"
    ],
    benefits: [
      "Competitive salary and benefits package",
      "Flexible working hours and remote work options",
      "Health insurance and wellness programs",
      "Professional development and learning opportunities",
      "Collaborative and inclusive work environment"
    ]
  };
};

const JobDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isSaved, setIsSaved] = useState(false);
  const [isApplied, setIsApplied] = useState(false);
  
  const job = getJobById(id || "1");

  const handleSaveJob = () => {
    setIsSaved(!isSaved);
    // API call to save/unsave job would go here
  };

  const handleApplyJob = () => {
    setIsApplied(true);
    // API call to apply for job would go here
  };

  const handleShareJob = () => {
    // Implement share functionality
    navigator.clipboard.writeText(window.location.href);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Button 
        variant="ghost" 
        className="mb-6" 
        onClick={() => navigate("/candidate/jobs")}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Jobs
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl">{job.title}</CardTitle>
                  <div className="flex items-center gap-2 text-gray-600 mt-1">
                    <Building2 className="h-4 w-4" />
                    <span>{job.company}</span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleSaveJob}
                  >
                    {isSaved ? (
                      <BookmarkCheck className="h-4 w-4" />
                    ) : (
                      <Bookmark className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleShareJob}
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span>{job.location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Briefcase className="h-4 w-4 text-gray-500" />
                  <span>{job.experience}</span>
                </div>
                <div className="flex items-center gap-1">
                  <IndianRupee className="h-4 w-4 text-gray-500" />
                  <span>{job.salary}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>{job.jobType}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>Posted {job.postedDate}</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">Job Description</h3>
                <p className="text-gray-700">{job.description}</p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">Required Skills</h3>
                <div className="flex flex-wrap gap-2">
                  {job.skills.map((skill) => (
                    <Badge key={skill} variant="secondary">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>

              <Tabs defaultValue="responsibilities" className="w-full">
                <TabsList className="grid grid-cols-3 w-full">
                  <TabsTrigger value="responsibilities">Responsibilities</TabsTrigger>
                  <TabsTrigger value="requirements">Requirements</TabsTrigger>
                  <TabsTrigger value="benefits">Benefits</TabsTrigger>
                </TabsList>
                <TabsContent value="responsibilities" className="mt-4">
                  <ul className="list-disc pl-5 space-y-2">
                    {job.responsibilities.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </TabsContent>
                <TabsContent value="requirements" className="mt-4">
                  <ul className="list-disc pl-5 space-y-2">
                    {job.requirements.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </TabsContent>
                <TabsContent value="benefits" className="mt-4">
                  <ul className="list-disc pl-5 space-y-2">
                    {job.benefits.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex justify-between">
              {isApplied ? (
                <Button className="w-full" disabled>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Applied Successfully
                </Button>
              ) : (
                <Button className="w-full" onClick={handleApplyJob}>
                  Apply Now
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>About {job.company}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{job.companyDescription}</p>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-gray-500" />
                  <span>{job.companySize}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4 text-gray-500" />
                  <span>{job.industry}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Similar Jobs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="cursor-pointer hover:bg-gray-50 p-2 rounded-md">
                    <h4 className="font-medium">Frontend Developer</h4>
                    <p className="text-sm text-gray-500">Digital Solutions Inc.</p>
                    <div className="flex items-center gap-2 mt-1 text-sm">
                      <MapPin className="h-3 w-3" />
                      <span>Mumbai</span>
                      <span>•</span>
                      <IndianRupee className="h-3 w-3" />
                      <span>20-30 LPA</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default JobDetail; 