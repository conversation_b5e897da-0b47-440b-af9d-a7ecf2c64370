import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { FileText, Clock, Award, CheckCircle, ChevronRight, Zap } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useData } from "@/context/DataContext";
import { cn } from "@/lib/utils";

// Mock test performance data for the chart
const performanceData = [
  { name: "Test 1", score: 75 },
  { name: "Test 2", score: 82 },
  { name: "Test 3", score: 78 },
  { name: "Test 4", score: 92 },
  { name: "Test 5", score: 85 },
];

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { jobs, referrals, tests, testSubmissions } = useData();
  const [activeTab, setActiveTab] = useState("overview");
  
  // Filter for the current candidate
  const candidateReferrals = referrals.filter(
    (referral) => referral.candidateId === user?.id
  );
  
  const candidateSubmissions = testSubmissions.filter(
    (sub) => sub.candidateId === user?.id
  );
  
  // Calculate some statistics
  const totalReferrals = candidateReferrals.length;
  const pendingReferrals = candidateReferrals.filter(
    (ref) => ref.status === "pending" || ref.status === "test_scheduled"
  ).length;
  const successfulReferrals = candidateReferrals.filter(
    (ref) => ref.status === "referred"
  ).length;
  
  const upcomingTests = candidateSubmissions.filter(
    (sub) => sub.status === "scheduled"
  );
  
  const completedTests = candidateSubmissions.filter(
    (sub) => sub.status === "completed"
  );
  
  const averageScore =
    completedTests.length > 0
      ? Math.round(
          completedTests.reduce((acc, curr) => acc + curr.score, 0) /
            completedTests.length
        )
      : 0;
  
  // Get the next scheduled test
  const nextTest = upcomingTests[0];
  
  // Calculate subscription tier statistics (mock data)
  const subscriptionUsage = {
    free: {
      used: 2,
      total: 3,
      percentage: 66,
    },
    small: {
      used: 4,
      total: 10,
      percentage: 40,
    },
    mid: {
      used: 8,
      total: 25,
      percentage: 32,
    },
    top: {
      used: 15,
      total: 50,
      percentage: 30,
    },
  };
  
  // Get subscription tier info
  const tierInfo = {
    free: {
      name: "Free",
      limit: 3,
      color: "text-gray-500",
    },
    small: {
      name: "Basic",
      limit: 10,
      color: "text-blue-500",
    },
    mid: {
      name: "Pro",
      limit: 25,
      color: "text-purple-500",
    },
    top: {
      name: "Enterprise",
      limit: 50,
      color: "text-refermitra-500",
    },
  };
  
  const userTier = user?.subscriptionTier || "free";
  const tierData = subscriptionUsage[userTier];
  const tier = tierInfo[userTier];

  const savedJobs = [];

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container max-w-7xl mx-auto">
        <div className="mb-8 animate-in fade-in duration-1000">
          <h1 className="text-3xl font-bold">Candidate Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Manage your tests and job referrals
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* Summary Cards */}
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Test Score Average
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Award className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{averageScore}%</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <FileText className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{totalReferrals}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-300">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Pending Tests
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Clock className="mr-2 h-5 w-5 text-amber-500" />
                <span className="text-2xl font-bold">{upcomingTests.length}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-400">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Successful Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
                <span className="text-2xl font-bold">{successfulReferrals}</span>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="animate-in fade-in duration-1000 delay-500"
        >
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Jobs</TabsTrigger>
            <TabsTrigger value="tests">Tests</TabsTrigger>
            <TabsTrigger value="referrals">Referrals</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Test Performance</CardTitle>
                    <CardDescription>
                      Your scores across different test assessments
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={performanceData}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis domain={[0, 100]} />
                          <Tooltip />
                          <Line
                            type="monotone"
                            dataKey="score"
                            name="Score (%)"
                            stroke="#0c87e0"
                            strokeWidth={2}
                            activeDot={{ r: 8 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Status</CardTitle>
                    <CardDescription>
                      {tier.name} Plan • {tierData.used}/{tierData.total} Tests Used
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Test Usage</span>
                          <span className="font-medium">
                            {tierData.used} of {tierData.total}
                          </span>
                        </div>
                        <Progress value={tierData.percentage} className="h-2" />
                      </div>
                      
                      {userTier !== "top" && (
                        <Button asChild variant="outline" className="w-full">
                          <Link to="/pricing">Upgrade Plan</Link>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
                
                {nextTest && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle>Next Scheduled Test</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {nextTest ? (
                        <div>
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium">
                                {tests.find((t) => t.id === nextTest.testId)?.title || "Test"}
                              </h3>
                              <p className="text-xs text-muted-foreground mt-1">
                                Scheduled: Tomorrow, 10:00 AM
                              </p>
                            </div>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                              Upcoming
                            </span>
                          </div>
                          <Button
                            asChild
                            className="w-full mt-4 bg-refermitra-500 hover:bg-refermitra-600"
                          >
                            <Link to={`/candidate/tests/${nextTest.id}`}>
                              Prepare for Test
                              <Zap className="ml-2 h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      ) : (
                        <p className="text-center py-4 text-muted-foreground">
                          No upcoming tests scheduled
                        </p>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="jobs" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Job Search</CardTitle>
                <CardDescription>
                  Find and apply for jobs that match your skills and experience
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    Browse through our curated list of job opportunities and apply directly through our platform.
                  </p>
                  <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                    <Link to="/candidate/jobs">
                      Browse Jobs
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Saved Jobs</CardTitle>
                <CardDescription>
                  Jobs you've saved for later
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {savedJobs.length > 0 ? (
                    <div className="space-y-4">
                      {savedJobs.map((job) => (
                        <div key={job.id} className="flex justify-between items-center p-4 border rounded-lg">
                          <div>
                            <h3 className="font-medium">{job.title}</h3>
                            <p className="text-sm text-muted-foreground">{job.company}</p>
                          </div>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-muted-foreground mb-4">You haven't saved any jobs yet.</p>
                      <Button asChild variant="outline">
                        <Link to="/candidate/jobs">Find Jobs</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="tests">
            <Card>
              <CardHeader>
                <CardTitle>Your Tests</CardTitle>
                <CardDescription>
                  All your test assessments, past and upcoming
                </CardDescription>
              </CardHeader>
              <CardContent>
                {candidateSubmissions.length > 0 ? (
                  <div className="rounded-md border">
                    <table className="min-w-full divide-y divide-border">
                      <thead className="bg-muted/50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Test
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Score
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Completion Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-card divide-y divide-border">
                        {candidateSubmissions.map((submission) => {
                          const test = tests.find((t) => t.id === submission.testId);
                          return (
                            <tr key={submission.id} className="hover:bg-muted/50 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="font-medium">{test?.title}</div>
                                <div className="text-xs text-muted-foreground">
                                  {test?.type === "coding" ? "Coding Challenge" : "Multiple Choice"}
                                  {" • "}
                                  {test?.difficulty} difficulty
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span
                                  className={cn(
                                    "inline-flex text-xs py-1 px-2 rounded-full",
                                    submission.status === "completed" && "bg-green-100 text-green-800",
                                    submission.status === "in_progress" && "bg-blue-100 text-blue-800",
                                    submission.status === "scheduled" && "bg-amber-100 text-amber-800",
                                    submission.status === "expired" && "bg-red-100 text-red-800"
                                  )}
                                >
                                  {submission.status.replace("_", " ")}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {submission.status === "completed" ? (
                                  <span className="font-medium">{submission.score}%</span>
                                ) : (
                                  <span className="text-muted-foreground">—</span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                                {submission.completedAt
                                  ? new Date(submission.completedAt).toLocaleDateString()
                                  : "—"}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <Button
                                  asChild
                                  variant="outline"
                                  size="sm"
                                  className="h-8 text-xs"
                                >
                                  <Link to={`/candidate/tests/${submission.id}`}>
                                    {submission.status === "scheduled"
                                      ? "Take Test"
                                      : "View Details"}
                                  </Link>
                                </Button>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground mb-4">No tests found</p>
                    <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                      <Link to="/candidate/tests">Browse Available Tests</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button asChild variant="outline">
                  <Link to="/candidate/tests">
                    View All Tests
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="referrals">
            <Card>
              <CardHeader>
                <CardTitle>Your Referrals</CardTitle>
                <CardDescription>
                  Track all your job referral applications
                </CardDescription>
              </CardHeader>
              <CardContent>
                {candidateReferrals.length > 0 ? (
                  <div className="rounded-md border">
                    <table className="min-w-full divide-y divide-border">
                      <thead className="bg-muted/50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Job
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Test Score
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Referral Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-card divide-y divide-border">
                        {candidateReferrals.map((referral) => {
                          const job = jobs.find((j) => j.id === referral.jobId);
                          const test = tests.find((t) => t.id === referral.testId);
                          return (
                            <tr key={referral.id} className="hover:bg-muted/50 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="font-medium">{job?.title}</div>
                                <div className="text-xs text-muted-foreground">{job?.company}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span
                                  className={cn(
                                    "inline-flex text-xs py-1 px-2 rounded-full",
                                    referral.status === "referred" && "bg-green-100 text-green-800",
                                    referral.status === "test_completed" && "bg-blue-100 text-blue-800",
                                    referral.status === "test_scheduled" && "bg-amber-100 text-amber-800",
                                    referral.status === "pending" && "bg-gray-100 text-gray-800",
                                    referral.status === "rejected" && "bg-red-100 text-red-800"
                                  )}
                                >
                                  {referral.status.replace("_", " ")}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {referral.testScore ? (
                                  <span className="font-medium">{referral.testScore}%</span>
                                ) : (
                                  <span className="text-muted-foreground">N/A</span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                                {referral.referredOn || "Pending"}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <Button
                                  asChild
                                  variant="outline"
                                  size="sm"
                                  className="h-8 text-xs"
                                >
                                  <Link to={`/candidate/referrals/${referral.id}`}>
                                    View Details
                                  </Link>
                                </Button>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground mb-4">No referrals found</p>
                    <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                      <Link to="/candidate/explore">Find Job Opportunities</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button asChild variant="outline">
                  <Link to="/candidate/referrals">
                    View All Referrals
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
