import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { useData } from "@/context/DataContext";
import { cn } from "@/lib/utils";

const Tests: React.FC = () => {
  const { tests } = useData();

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Tests</h1>
            <p className="text-muted-foreground mt-2">
              Manage job referral assessment tests
            </p>
          </div>
          <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
            <Link to="/admin/tests/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Test
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Tests Management</CardTitle>
            <CardDescription>
              Create and manage test assessments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted/50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Test Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Difficulty
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Duration
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-card divide-y divide-border">
                  {tests.map((test) => (
                    <tr key={test.id} className="hover:bg-muted/50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium">{test.title}</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Created by: Admin
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={cn(
                            "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                            test.type === "coding" && "bg-blue-100 text-blue-800",
                            test.type === "mcq" && "bg-green-100 text-green-800",
                            test.type === "personality" && "bg-purple-100 text-purple-800"
                          )}
                        >
                          {test.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={cn(
                            "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                            test.difficulty === "easy" && "bg-green-100 text-green-800",
                            test.difficulty === "medium" && "bg-amber-100 text-amber-800",
                            test.difficulty === "hard" && "bg-red-100 text-red-800"
                          )}
                        >
                          {test.difficulty}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">{test.duration} mins</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={cn(
                            "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                            test.status === "active" && "bg-green-100 text-green-800",
                            test.status === "draft" && "bg-gray-100 text-gray-800",
                            test.status === "completed" && "bg-blue-100 text-blue-800"
                          )}
                        >
                          {test.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 text-xs"
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 text-xs"
                          >
                            View
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4">
            <div className="text-sm text-muted-foreground">
              Showing {tests.length} tests
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Tests; 