import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Bar<PERSON>hart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { Users, CheckCircle, FileText, Calendar, ChevronRight, FileCode, Plus } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useData } from "@/context/DataContext";
import { cn } from "@/lib/utils";

// Mock data for the charts
const userTypeData = [
  { name: "Candidates", value: 65 },
  { name: "Providers", value: 35 },
];

const referralStatusData = [
  { name: "Pending", value: 15 },
  { name: "Test Scheduled", value: 25 },
  { name: "Test Completed", value: 35 },
  { name: "Referred", value: 18 },
  { name: "Rejected", value: 7 },
];

const monthlyActivityData = [
  { name: "Jan", users: 30, tests: 20, referrals: 15 },
  { name: "Feb", users: 40, tests: 30, referrals: 25 },
  { name: "Mar", users: 35, tests: 25, referrals: 20 },
  { name: "Apr", users: 50, tests: 40, referrals: 30 },
  { name: "May", users: 65, tests: 55, referrals: 45 },
  { name: "Jun", users: 80, tests: 70, referrals: 60 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { jobs, referrals, tests, testSubmissions } = useData();
  const [activeTab, setActiveTab] = useState("overview");
  const navigate = useNavigate();
  
  // Calculate statistics
  const totalJobs = jobs.length;
  const totalTests = tests.length;
  const totalReferrals = referrals.length;
  const completedTests = testSubmissions.filter(
    (sub) => sub.status === "completed"
  ).length;
  
  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container max-w-7xl mx-auto">
        <div className="mb-8 animate-in fade-in duration-1000">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Platform overview and analytics
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* Summary Cards */}
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">100</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Tests
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <FileCode className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{totalTests}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-300">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Jobs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <FileText className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{totalJobs}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-400">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <CheckCircle className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{totalReferrals}</span>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="animate-in fade-in duration-1000 delay-500"
        >
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="tests">Tests</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Activity</CardTitle>
                <CardDescription>
                  User, test, and referral activity over the past 6 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={monthlyActivityData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="users" name="New Users" fill="#0c87e0" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="tests" name="Tests Taken" fill="#6E59A5" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="referrals" name="Referrals" fill="#00C49F" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Types</CardTitle>
                  <CardDescription>
                    Distribution of candidates vs referral providers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={userTypeData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {userTypeData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => `${value}`} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/admin/users">
                      View User Details
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Referral Status</CardTitle>
                  <CardDescription>
                    Current status of all referrals on the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={referralStatusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {referralStatusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => `${value}`} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/admin/referrals">
                      View Referral Details
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Upcoming Scheduled Tests</CardTitle>
                  <CardDescription>
                    Tests scheduled for the next 7 days
                  </CardDescription>
                </div>
                <Button asChild>
                  <Link to="/admin/tests/create">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Test
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <table className="min-w-full divide-y divide-border">
                    <thead className="bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Test Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Candidate
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Schedule
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-card divide-y divide-border">
                      {testSubmissions
                        .filter((sub) => sub.status === "scheduled")
                        .slice(0, 5)
                        .map((submission) => {
                          const test = tests.find((t) => t.id === submission.testId);
                          return (
                            <tr key={submission.id} className="hover:bg-muted/50 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="font-medium">{test?.title}</div>
                                <div className="text-xs text-muted-foreground">
                                  {test?.difficulty} • {test?.duration} mins
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="font-medium">{submission.candidateName}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                                  <span>Tomorrow, 10:00 AM</span>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                  Scheduled
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button asChild variant="outline" className="w-full">
                  <Link to="/admin/tests">
                    View All Tests
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>Platform Users</CardTitle>
                <CardDescription>
                  View and manage users registered on the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <table className="min-w-full divide-y divide-border">
                    <thead className="bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          User
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Role
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Email
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Plan
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-card divide-y divide-border">
                      {[
                        {
                          id: "1",
                          name: "John Provider",
                          email: "<EMAIL>",
                          role: "provider",
                          status: "active",
                          plan: "top",
                        },
                        {
                          id: "2",
                          name: "Jane Candidate",
                          email: "<EMAIL>",
                          role: "candidate",
                          status: "active",
                          plan: "free",
                        },
                        {
                          id: "3",
                          name: "Admin User",
                          email: "<EMAIL>",
                          role: "admin",
                          status: "active",
                          plan: "top",
                        },
                        {
                          id: "4",
                          name: "Alice Johnson",
                          email: "<EMAIL>",
                          role: "candidate",
                          status: "active",
                          plan: "mid",
                        },
                        {
                          id: "5",
                          name: "Bob Smith",
                          email: "<EMAIL>",
                          role: "provider",
                          status: "active",
                          plan: "small",
                        },
                      ].map((user) => (
                        <tr key={user.id} className="hover:bg-muted/50 transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium">{user.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                user.role === "provider" && "bg-blue-100 text-blue-800",
                                user.role === "candidate" && "bg-green-100 text-green-800",
                                user.role === "admin" && "bg-purple-100 text-purple-800"
                              )}
                            >
                              {user.role}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm">{user.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {user.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                user.plan === "free" && "bg-gray-100 text-gray-800",
                                user.plan === "small" && "bg-blue-100 text-blue-800",
                                user.plan === "mid" && "bg-purple-100 text-purple-800",
                                user.plan === "top" && "bg-refermitra-100 text-refermitra-800"
                              )}
                            >
                              {user.plan}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 text-xs"
                            >
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing 5 of 100 users
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" disabled>
                    Previous
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="tests" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-semibold">Tests</h2>
                <p className="text-muted-foreground">
                  Manage job referral assessment tests
                </p>
              </div>
            </div>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Tests Management</CardTitle>
                  <CardDescription>
                    Create and manage test assessments
                  </CardDescription>
                </div>
                <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                  <Link to="/admin/tests/create">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Test
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <table className="min-w-full divide-y divide-border">
                    <thead className="bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Test Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Difficulty
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Duration
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-card divide-y divide-border">
                      {tests.map((test) => (
                        <tr key={test.id} className="hover:bg-muted/50 transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium">{test.title}</div>
                            <div className="text-xs text-muted-foreground mt-1">
                              Created by: Admin
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                test.type === "coding" && "bg-blue-100 text-blue-800",
                                test.type === "mcq" && "bg-green-100 text-green-800",
                                test.type === "personality" && "bg-purple-100 text-purple-800"
                              )}
                            >
                              {test.type}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                test.difficulty === "easy" && "bg-green-100 text-green-800",
                                test.difficulty === "medium" && "bg-amber-100 text-amber-800",
                                test.difficulty === "hard" && "bg-red-100 text-red-800"
                              )}
                            >
                              {test.difficulty}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm">{test.duration} mins</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                test.status === "active" && "bg-green-100 text-green-800",
                                test.status === "draft" && "bg-gray-100 text-gray-800",
                                test.status === "completed" && "bg-blue-100 text-blue-800"
                              )}
                            >
                              {test.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 text-xs"
                              >
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 text-xs"
                              >
                                View
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {tests.length} tests
                </div>
                <Button asChild variant="outline">
                  <Link to="/admin/tests">
                    View All Tests
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
