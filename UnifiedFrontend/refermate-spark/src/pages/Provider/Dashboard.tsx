
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { PlusCircle, Briefcase, Users, Clock, FilePlus, ArrowUpRight, ChevronRight } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useData } from "@/context/DataContext";
import { cn } from "@/lib/utils";

// Mock activity data for the chart
const activityData = [
  { name: "Mon", referrals: 2, tests: 3 },
  { name: "<PERSON><PERSON>", referrals: 1, tests: 2 },
  { name: "<PERSON><PERSON>", referrals: 4, tests: 5 },
  { name: "<PERSON><PERSON>", referrals: 3, tests: 2 },
  { name: "<PERSON><PERSON>", referrals: 5, tests: 7 },
  { name: "<PERSON><PERSON>", referrals: 2, tests: 1 },
  { name: "Sun", referrals: 0, tests: 0 },
];

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { jobs, referrals, testSubmissions } = useData();
  const [activeTab, setActiveTab] = useState("overview");
  
  // Filter out jobs for the current provider
  const providerJobs = jobs.filter((job) => job.postedBy === user?.id);
  
  // Filter referrals made by the current provider
  const providerReferrals = referrals.filter((referral) => referral.providerId === user?.id);
  
  // Calculate some statistics
  const totalJobs = providerJobs.length;
  const totalReferrals = providerReferrals.length;
  const pendingReferrals = providerReferrals.filter(
    (ref) => ref.status === "pending" || ref.status === "test_scheduled"
  ).length;
  const completedReferrals = providerReferrals.filter(
    (ref) => ref.status === "test_completed" || ref.status === "referred"
  ).length;
  
  // Get recent referrals
  const recentReferrals = [...providerReferrals]
    .sort((a, b) => {
      if (a.referredOn && b.referredOn) {
        return new Date(b.referredOn).getTime() - new Date(a.referredOn).getTime();
      }
      return a.id.localeCompare(b.id);
    })
    .slice(0, 5);
  
  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container max-w-7xl mx-auto">
        <div className="mb-8 animate-in fade-in duration-1000">
          <h1 className="text-3xl font-bold">Provider Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Manage your job postings and referrals
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* Summary Cards */}
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Jobs Posted
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Briefcase className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{totalJobs}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-refermitra-500" />
                <span className="text-2xl font-bold">{totalReferrals}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-300">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Pending Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Clock className="mr-2 h-5 w-5 text-amber-500" />
                <span className="text-2xl font-bold">{pendingReferrals}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card className="animate-in fade-in scale-in-center duration-1000 delay-400">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Completed Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <FilePlus className="mr-2 h-5 w-5 text-green-500" />
                <span className="text-2xl font-bold">{completedReferrals}</span>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="animate-in fade-in duration-1000 delay-500"
        >
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="referrals">Recent Referrals</TabsTrigger>
            <TabsTrigger value="jobs">Your Jobs</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Weekly Activity</CardTitle>
                <CardDescription>
                  Your referrals and test activity for the past week
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={activityData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="referrals" name="Referrals" fill="#0c87e0" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="tests" name="Tests" fill="#6E59A5" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Referrals</CardTitle>
                  <CardDescription>
                    Your most recent candidate referrals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {recentReferrals.length > 0 ? (
                    <ul className="space-y-4">
                      {recentReferrals.map((referral) => {
                        const relatedJob = jobs.find((job) => job.id === referral.jobId);
                        return (
                          <li
                            key={referral.id}
                            className="flex items-center justify-between p-3 rounded-lg bg-secondary/50 hover:bg-secondary transition-colors"
                          >
                            <div>
                              <p className="font-medium">Candidate #{referral.candidateId}</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {relatedJob?.title}
                              </p>
                            </div>
                            <div className="flex items-center">
                              <span
                                className={cn(
                                  "text-xs py-1 px-2 rounded-full",
                                  referral.status === "referred" && "bg-green-100 text-green-800",
                                  referral.status === "test_completed" && "bg-blue-100 text-blue-800",
                                  referral.status === "test_scheduled" && "bg-amber-100 text-amber-800",
                                  referral.status === "pending" && "bg-gray-100 text-gray-800",
                                  referral.status === "rejected" && "bg-red-100 text-red-800"
                                )}
                              >
                                {referral.status.replace("_", " ")}
                              </span>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  ) : (
                    <p className="text-center text-muted-foreground py-8">
                      No referrals yet. Start by posting a job and referring candidates.
                    </p>
                  )}
                </CardContent>
                <CardFooter className="border-t pt-4 flex justify-between">
                  <Button asChild variant="ghost" size="sm" className="text-xs">
                    <Link to="/provider/referrals">
                      View all
                      <ChevronRight className="ml-1 h-3 w-3" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Your Jobs</CardTitle>
                  <CardDescription>
                    Jobs you've posted for referrals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {providerJobs.length > 0 ? (
                    <ul className="space-y-4">
                      {providerJobs.map((job) => (
                        <li
                          key={job.id}
                          className="flex items-center justify-between p-3 rounded-lg bg-secondary/50 hover:bg-secondary transition-colors"
                        >
                          <div>
                            <p className="font-medium">{job.title}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {job.company} • {job.location}
                            </p>
                          </div>
                          <div className="flex items-center">
                            <span className="flex items-center text-xs mr-4">
                              <Users className="mr-1 h-3 w-3 text-refermitra-400" />
                              {job.referrals}
                            </span>
                            <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-muted-foreground py-8">
                      No jobs posted yet. Add your first job listing.
                    </p>
                  )}
                </CardContent>
                <CardFooter className="border-t pt-4 flex justify-between">
                  <Button asChild variant="ghost" size="sm" className="text-xs">
                    <Link to="/provider/jobs">
                      View all
                      <ChevronRight className="ml-1 h-3 w-3" />
                    </Link>
                  </Button>
                  
                  <Button asChild size="sm" className="text-xs bg-refermitra-500 hover:bg-refermitra-600">
                    <Link to="/provider/jobs/new">
                      <PlusCircle className="mr-1 h-3 w-3" />
                      Add job
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="referrals">
            <Card>
              <CardHeader>
                <CardTitle>Recent Referrals</CardTitle>
                <CardDescription>
                  Manage and track all your candidate referrals
                </CardDescription>
              </CardHeader>
              <CardContent>
                {providerReferrals.length > 0 ? (
                  <div className="rounded-md border">
                    <table className="min-w-full divide-y divide-border">
                      <thead className="bg-muted/50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Candidate
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Job
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Test Score
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Date
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-card divide-y divide-border">
                        {providerReferrals.map((referral) => {
                          const job = jobs.find((j) => j.id === referral.jobId);
                          return (
                            <tr key={referral.id} className="hover:bg-muted/50 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="font-medium">Candidate #{referral.candidateId}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div>{job?.title}</div>
                                <div className="text-xs text-muted-foreground">{job?.company}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span
                                  className={cn(
                                    "inline-flex text-xs py-1 px-2 rounded-full",
                                    referral.status === "referred" && "bg-green-100 text-green-800",
                                    referral.status === "test_completed" && "bg-blue-100 text-blue-800",
                                    referral.status === "test_scheduled" && "bg-amber-100 text-amber-800",
                                    referral.status === "pending" && "bg-gray-100 text-gray-800",
                                    referral.status === "rejected" && "bg-red-100 text-red-800"
                                  )}
                                >
                                  {referral.status.replace("_", " ")}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {referral.testScore ? (
                                  <span className="font-medium">{referral.testScore}%</span>
                                ) : (
                                  <span className="text-muted-foreground">N/A</span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                                {referral.referredOn || "Pending"}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground mb-4">No referrals found</p>
                    <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                      <Link to="/provider/jobs">Browse Jobs to Refer</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button asChild variant="outline">
                  <Link to="/provider/referrals">
                    View All Referrals
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="jobs">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Your Jobs</CardTitle>
                  <CardDescription>
                    Manage job listings for referrals
                  </CardDescription>
                </div>
                <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                  <Link to="/provider/jobs/new">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Post New Job
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                {providerJobs.length > 0 ? (
                  <div className="space-y-4">
                    {providerJobs.map((job) => (
                      <div
                        key={job.id}
                        className="p-4 rounded-lg border border-border hover:shadow-soft transition-shadow"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{job.title}</h3>
                            <p className="text-sm text-muted-foreground mt-1">
                              {job.company} • {job.location}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center text-sm">
                              <Users className="mr-1 h-4 w-4 text-refermitra-400" />
                              <span>{job.referrals} referrals</span>
                            </div>
                          </div>
                        </div>
                        <p className="text-sm mt-2 line-clamp-2">
                          {job.description}
                        </p>
                        <div className="mt-4 flex flex-wrap gap-2">
                          {job.requirements.slice(0, 3).map((req, index) => (
                            <span
                              key={index}
                              className="text-xs px-2 py-1 bg-secondary rounded-full"
                            >
                              {req}
                            </span>
                          ))}
                          {job.requirements.length > 3 && (
                            <span className="text-xs px-2 py-1 bg-secondary rounded-full">
                              +{job.requirements.length - 3} more
                            </span>
                          )}
                        </div>
                        <div className="mt-4 flex justify-between items-center text-xs text-muted-foreground">
                          <span>Posted: {job.postedDate}</span>
                          <Button variant="ghost" size="sm" className="h-8 text-xs">
                            View Details
                            <ChevronRight className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground mb-4">No jobs posted yet</p>
                    <Button asChild className="bg-refermitra-500 hover:bg-refermitra-600">
                      <Link to="/provider/jobs/new">Post Your First Job</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button asChild variant="outline" className="w-full">
                  <Link to="/provider/jobs">View All Jobs</Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
