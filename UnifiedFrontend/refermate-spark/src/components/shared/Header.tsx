import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Menu, 
  X, 
  LogOut, 
  User, 
  Settings, 
  BellIcon, 
  ChevronDown 
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useData } from "@/context/DataContext";
import NotificationDropdown from "./NotificationDropdown";

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const { notifications } = useData();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [headerVisible, setHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Filter notifications for current user
  const userNotifications = user
    ? notifications.filter(n => n.userId === user.id && !n.read)
    : [];

  // Handle scroll behavior for elegant header transition
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY) {
        setHeaderVisible(false);
      } else {
        setHeaderVisible(true);
      }
      
      setScrollY(currentScrollY);
      setLastScrollY(currentScrollY);
    };
    
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header
      className={cn(
        "fixed w-full z-50 transition-all duration-300 ease-apple bg-background border-b",
        scrollY > 0 ? "shadow-sm" : "",
        !headerVisible && scrollY > 100 ? "-translate-y-full" : "translate-y-0"
      )}
    >
      <div className="container max-w-7xl mx-auto px-4 sm:px-6">
        <div className="flex justify-between items-center h-24">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img
                src="/src/assets/images/logo.svg"
                alt="ReferMitra"
                className="h-16 w-auto hover:opacity-90 transition-opacity"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link
              to="/"
              className={cn(
                "text-base font-medium transition-colors hover:text-refermitra-500",
                isActive("/") ? "text-refermitra-500" : "text-foreground"
              )}
            >
              Home
            </Link>
            
            {user ? (
              <>
                {user.role === "provider" && (
                  <>
                    <Link
                      to="/provider"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/provider") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/provider/jobs"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/provider/jobs") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Jobs
                    </Link>
                    <Link
                      to="/provider/referrals"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/provider/referrals") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Referrals
                    </Link>
                  </>
                )}
                
                {user.role === "candidate" && (
                  <>
                    <Link
                      to="/candidate"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/candidate/jobs"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate/jobs") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Jobs
                    </Link>
                    <Link
                      to="/candidate/tests"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate/tests") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Tests
                    </Link>
                    <Link
                      to="/candidate/referrals"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate/referrals") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Referrals
                    </Link>
                  </>
                )}
                
                {user.role === "admin" && (
                  <>
                    <Link
                      to="/admin"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/admin/tests"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin/tests") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Tests
                    </Link>
                    <Link
                      to="/admin/users"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin/users") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Users
                    </Link>
                    <Link
                      to="/admin/referrals"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin/referrals") ? "text-refermitra-500" : "text-foreground"
                      )}
                    >
                      Referrals
                    </Link>
                  </>
                )}
              </>
            ) : (
              <>
                <Link
                  to="/about"
                  className={cn(
                    "text-base font-medium transition-colors hover:text-refermitra-500",
                    isActive("/about") ? "text-refermitra-500" : "text-foreground"
                  )}
                >
                  About
                </Link>
                <Link
                  to="/pricing"
                  className={cn(
                    "text-base font-medium transition-colors hover:text-refermitra-500",
                    isActive("/pricing") ? "text-refermitra-500" : "text-foreground"
                  )}
                >
                  Pricing
                </Link>
              </>
            )}
          </nav>

          <div className="flex items-center space-x-4">
            {user ? (
              <>
                <div className="hidden md:flex items-center space-x-4">
                  <NotificationDropdown notifications={userNotifications} />
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="relative p-0 h-10 w-10 rounded-full overflow-hidden border-2 border-transparent hover:border-refermitra-500 transition-colors">
                        <img
                          src={user.avatar || "https://i.pravatar.cc/150?img=default"}
                          alt={user.name}
                          className="h-full w-full object-cover"
                        />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel>
                        <div className="flex flex-col space-y-1">
                          <p className="font-medium text-sm">{user.name}</p>
                          <p className="text-xs text-muted-foreground">{user.email}</p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link to="/profile" className="flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          Profile
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/settings" className="flex items-center">
                          <Settings className="mr-2 h-4 w-4" />
                          Settings
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={logout} className="text-red-600">
                        <LogOut className="mr-2 h-4 w-4" />
                        Logout
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Mobile Menu Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                >
                  {mobileMenuOpen ? (
                    <X className="h-6 w-6" />
                  ) : (
                    <Menu className="h-6 w-6" />
                  )}
                </Button>
              </>
            ) : (
              <div className="flex items-center space-x-4">
                <Button variant="ghost" asChild>
                  <Link to="/login">Login</Link>
                </Button>
                <Button asChild>
                  <Link to="/register">Register</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn(
          "md:hidden fixed inset-0 bg-background/95 backdrop-blur-sm z-40 transition-all duration-300",
          mobileMenuOpen ? "translate-y-16" : "-translate-y-full"
        )}
      >
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 py-4">
          <nav className="flex flex-col space-y-4">
            <Link
              to="/"
              className={cn(
                "text-base font-medium transition-colors hover:text-refermitra-500",
                isActive("/") ? "text-refermitra-500" : "text-foreground"
              )}
              onClick={() => setMobileMenuOpen(false)}
            >
              Home
            </Link>
            
            {user ? (
              <>
                {user.role === "provider" && (
                  <>
                    <Link
                      to="/provider"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/provider") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/provider/jobs"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/provider/jobs") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Jobs
                    </Link>
                    <Link
                      to="/provider/referrals"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/provider/referrals") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Referrals
                    </Link>
                  </>
                )}
                
                {user.role === "candidate" && (
                  <>
                    <Link
                      to="/candidate"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/candidate/jobs"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate/jobs") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Jobs
                    </Link>
                    <Link
                      to="/candidate/tests"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate/tests") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Tests
                    </Link>
                    <Link
                      to="/candidate/referrals"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/candidate/referrals") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Referrals
                    </Link>
                  </>
                )}
                
                {user.role === "admin" && (
                  <>
                    <Link
                      to="/admin"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/admin/tests"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin/tests") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Tests
                    </Link>
                    <Link
                      to="/admin/users"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin/users") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Users
                    </Link>
                    <Link
                      to="/admin/referrals"
                      className={cn(
                        "text-base font-medium transition-colors hover:text-refermitra-500",
                        isActive("/admin/referrals") ? "text-refermitra-500" : "text-foreground"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Referrals
                    </Link>
                  </>
                )}
              </>
            ) : (
              <>
                <Link
                  to="/about"
                  className={cn(
                    "text-base font-medium transition-colors hover:text-refermitra-500",
                    isActive("/about") ? "text-refermitra-500" : "text-foreground"
                  )}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  About
                </Link>
                <Link
                  to="/pricing"
                  className={cn(
                    "text-base font-medium transition-colors hover:text-refermitra-500",
                    isActive("/pricing") ? "text-refermitra-500" : "text-foreground"
                  )}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Pricing
                </Link>
              </>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
