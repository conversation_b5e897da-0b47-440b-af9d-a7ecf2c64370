
import React from "react";
import { <PERSON> } from "react-router-dom";
import { BellIcon, CheckIcon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  read: boolean;
  createdAt: string;
  link?: string;
}

interface NotificationDropdownProps {
  notifications: Notification[];
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <BellIcon className="h-5 w-5" />
          {notifications.length > 0 && (
            <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-refermitra-500 animate-pulse-subtle" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80" sideOffset={8}>
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>Notifications</span>
          {notifications.length > 0 && (
            <span className="text-xs bg-refermitra-500 text-white px-2 py-0.5 rounded-full">
              {notifications.length}
            </span>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <div className="max-h-[300px] overflow-y-auto">
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <DropdownMenuItem key={notification.id} asChild>
                <Link
                  to={notification.link || "#"}
                  className={cn(
                    "flex flex-col p-3 cursor-pointer hover:bg-accent",
                    !notification.read && "bg-refermitra-50"
                  )}
                >
                  <div className="flex justify-between items-start">
                    <span className="font-medium text-sm">{notification.title}</span>
                    <span className={cn(
                      "text-xs rounded-full px-2 py-0.5",
                      notification.type === "info" && "bg-blue-100 text-blue-700",
                      notification.type === "success" && "bg-green-100 text-green-700",
                      notification.type === "warning" && "bg-yellow-100 text-yellow-700",
                      notification.type === "error" && "bg-red-100 text-red-700"
                    )}>
                      {notification.type}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{notification.message}</p>
                  <span className="text-xs text-muted-foreground mt-2">
                    {new Date(notification.createdAt).toLocaleDateString()} {new Date(notification.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </Link>
              </DropdownMenuItem>
            ))
          ) : (
            <div className="p-4 text-center text-sm text-muted-foreground">
              <div className="flex justify-center mb-2">
                <CheckIcon className="h-6 w-6 text-green-500" />
              </div>
              <p>You're all caught up!</p>
              <p className="text-xs mt-1">No new notifications</p>
            </div>
          )}
        </div>
        
        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="justify-center text-xs text-refermitra-500 hover:text-refermitra-600 cursor-pointer">
              Mark all as read
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationDropdown;
