
import React from "react";

const LoadingScreen: React.FC = () => {
  return (
    <div className="fixed inset-0 bg-background flex items-center justify-center z-50">
      <div className="flex flex-col items-center">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-refermitra-200 rounded-full"></div>
          <div className="absolute top-0 left-0 w-16 h-16 border-4 border-t-refermitra-500 rounded-full animate-spin"></div>
        </div>
        <div className="mt-6 animate-pulse-subtle">
          <h2 className="text-xl font-semibold text-refermitra-500">Refermitra</h2>
          <p className="text-sm text-muted-foreground mt-1 text-center">Loading experience...</p>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
