import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';

interface PricingPlan {
  name: string;
  price: {
    monthly: number;
    quarterly: number;
    yearly: number;
  };
  features: string[];
  highlighted?: boolean;
}

const plans: PricingPlan[] = [
  {
    name: 'Starter',
    price: {
      monthly: 59,
      quarterly: 159,
      yearly: 599,
    },
    features: [
      'Basic job listings',
      'Email support',
      '2 active job posts',
      'Basic candidate matching',
      'Standard job templates',
    ],
  },
  {
    name: 'Growth',
    price: {
      monthly: 259,
      quarterly: 699,
      yearly: 2499,
    },
    features: [
      'Advanced job listings',
      'Priority support',
      '10 active job posts',
      'AI-powered matching',
      'Custom job templates',
      'Analytics dashboard',
    ],
    highlighted: true,
  },
  {
    name: 'Premium',
    price: {
      monthly: 599,
      quarterly: 1599,
      yearly: 5999,
    },
    features: [
      'Unlimited job listings',
      '24/7 dedicated support',
      'Unlimited active posts',
      'Advanced AI matching',
      'Custom branding',
      'API access',
      'Team collaboration',
    ],
  },
];

type BillingCycle = 'monthly' | 'quarterly' | 'yearly';

export default function Pricing() {
  const [billingCycle, setBillingCycle] = useState<BillingCycle>('monthly');

  const getDiscount = (cycle: BillingCycle) => {
    switch (cycle) {
      case 'quarterly':
        return '10%';
      case 'yearly':
        return '20%';
      default:
        return '0%';
    }
  };

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="text-center mb-12 max-w-3xl mx-auto">
        <span className="text-primary font-semibold mb-2 inline-block">Pricing Plans</span>
        <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          Find the Perfect Plan for Your Recruitment Needs
        </h1>
        <p className="text-muted-foreground text-xl mb-4">
          Start small and scale as you grow. No hidden fees, cancel anytime.
        </p>
        <p className="text-sm text-muted-foreground mb-8">
          All plans include basic features like job posting, candidate matching, and email notifications.
        </p>
        <div className="flex items-center justify-center gap-4 mb-8">
          <span className={billingCycle === 'monthly' ? 'font-semibold' : ''}>Monthly</span>
          <Switch
            checked={billingCycle !== 'monthly'}
            onCheckedChange={(checked) => setBillingCycle(checked ? 'yearly' : 'monthly')}
          />
          <span className={billingCycle === 'yearly' ? 'font-semibold' : ''}>Yearly</span>
          {billingCycle === 'yearly' && (
            <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
              Save {getDiscount(billingCycle)}
            </span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {plans.map((plan) => (
          <Card
            key={plan.name}
            className={`relative ${
              plan.highlighted
                ? 'border-primary shadow-lg scale-105'
                : 'border-border'
            }`}
          >
            {plan.highlighted && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary text-primary-foreground text-sm font-medium px-3 py-1 rounded-full">
                  Most Popular
                </span>
              </div>
            )}
            <CardHeader>
              <h3 className="text-2xl font-bold">{plan.name}</h3>
              <div className="mt-4">
                <span className="text-4xl font-bold">
                  ₹{plan.price[billingCycle]}
                </span>
                <span className="text-muted-foreground">/{billingCycle}</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <svg
                      className="w-5 h-5 text-green-500 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
              <Button className="w-full mt-6" variant={plan.highlighted ? 'default' : 'outline'}>
                Get Started
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
