
import React, { useState, useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import Header from "@/components/shared/Header";
import Footer from "@/components/shared/Footer";
import LoadingScreen from "@/components/shared/LoadingScreen";

const MainLayout: React.FC = () => {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isPageLoading, setIsPageLoading] = useState(false); // Initialize as false to prevent flickering

  useEffect(() => {
    // Check if user is logged in
    if (!isLoading) {
      const publicRoutes = ["/login", "/register", "/"];
      const isPublicRoute = publicRoutes.some(route => location.pathname.startsWith(route));
      
      if (!user && !isPublicRoute) {
        navigate("/login");
      } else if (user && (location.pathname === "/login" || location.pathname === "/register")) {
        // Redirect to appropriate dashboard based on role
        if (user.role === "provider") {
          navigate("/provider");
        } else if (user.role === "candidate") {
          navigate("/candidate");
        } else if (user.role === "admin") {
          navigate("/admin");
        }
      }
    }
  }, [user, isLoading, location.pathname, navigate]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default MainLayout;
