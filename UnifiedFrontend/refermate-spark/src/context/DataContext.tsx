
import React, { createContext, useContext, useState } from "react";

export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  salary: string;
  description: string;
  requirements: string[];
  postedBy: string;
  postedDate: string;
  referrals: number;
}

export interface Test {
  id: string;
  title: string;
  description: string;
  type: "coding" | "mcq" | "personality";
  duration: number; // in minutes
  totalScore: number;
  difficulty: "easy" | "medium" | "hard";
  skillsAssessed: string[];
  createdBy: string;
  status: "draft" | "active" | "completed";
}

export interface TestSubmission {
  id: string;
  testId: string;
  candidateId: string;
  candidateName: string;
  score: number;
  completedAt: string;
  status: "scheduled" | "in_progress" | "completed" | "expired";
  feedback?: string;
}

export interface Referral {
  id: string;
  jobId: string;
  candidateId: string;
  providerId: string;
  testId: string;
  status: "pending" | "test_scheduled" | "test_completed" | "referred" | "rejected";
  testScore?: number;
  referredOn?: string;
  notes?: string;
}

interface DataContextType {
  jobs: Job[];
  tests: Test[];
  testSubmissions: TestSubmission[];
  referrals: Referral[];
  notifications: Notification[];
  addJob: (job: Omit<Job, "id" | "postedBy" | "postedDate" | "referrals">) => void;
  addReferral: (referral: Omit<Referral, "id">) => void;
  updateReferralStatus: (referralId: string, status: Referral["status"], data?: Partial<Referral>) => void;
  scheduleTest: (testId: string, candidateId: string) => void;
  completeTest: (submissionId: string, score: number) => void;
}

interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  read: boolean;
  createdAt: string;
  link?: string;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

// Mock data
const mockJobs: Job[] = [
  {
    id: "job1",
    title: "Senior Frontend Developer",
    company: "Tech Innovations Inc.",
    location: "San Francisco, CA (Remote)",
    salary: "$120,000 - $150,000",
    description: "We are looking for a Senior Frontend Developer to join our team. You will be responsible for developing and implementing user interface components using React.js and related technologies.",
    requirements: ["5+ years of experience with React", "Experience with TypeScript", "Experience with state management libraries", "Experience with testing frameworks"],
    postedBy: "1", // John Provider
    postedDate: "2023-06-15",
    referrals: 8,
  },
  {
    id: "job2",
    title: "Backend Developer",
    company: "CloudScale Systems",
    location: "New York, NY (Hybrid)",
    salary: "$110,000 - $140,000",
    description: "CloudScale is seeking a skilled Backend Developer to design and implement scalable APIs and microservices. You will work with a team of engineers to build robust backend systems.",
    requirements: ["3+ years of experience with Node.js", "Experience with database design", "Knowledge of RESTful API design", "Experience with cloud platforms"],
    postedBy: "1", // John Provider
    postedDate: "2023-06-20",
    referrals: 5,
  },
  {
    id: "job3",
    title: "Full Stack Developer",
    company: "Digital Solutions Ltd",
    location: "Austin, TX (On-site)",
    salary: "$100,000 - $130,000",
    description: "Join our team as a Full Stack Developer to work on exciting projects from concept to deployment. You will be involved in all aspects of development including frontend, backend, and database design.",
    requirements: ["Experience with full stack development", "Proficiency in JavaScript/TypeScript", "Knowledge of React and Node.js", "Experience with SQL and NoSQL databases"],
    postedBy: "1", // John Provider
    postedDate: "2023-06-25",
    referrals: 3,
  },
];

const mockTests: Test[] = [
  {
    id: "test1",
    title: "Frontend Developer Skills Assessment",
    description: "This test assesses core frontend development skills including JavaScript, React, CSS, and HTML.",
    type: "coding",
    duration: 90,
    totalScore: 100,
    difficulty: "medium",
    skillsAssessed: ["JavaScript", "React", "CSS", "HTML", "Problem Solving"],
    createdBy: "1", // John Provider
    status: "active",
  },
  {
    id: "test2",
    title: "Backend Development Challenge",
    description: "Test your backend development skills with challenges on API design, database optimization, and server architecture.",
    type: "coding",
    duration: 120,
    totalScore: 100,
    difficulty: "hard",
    skillsAssessed: ["Node.js", "Database Design", "API Development", "Authentication", "Performance"],
    createdBy: "1", // John Provider
    status: "active",
  },
  {
    id: "test3",
    title: "Software Engineering Principles",
    description: "Multiple choice assessment on software engineering principles, design patterns, and best practices.",
    type: "mcq",
    duration: 60,
    totalScore: 50,
    difficulty: "medium",
    skillsAssessed: ["Design Patterns", "Software Architecture", "Testing Methodologies", "System Design"],
    createdBy: "3", // Admin
    status: "active",
  },
];

const mockTestSubmissions: TestSubmission[] = [
  {
    id: "sub1",
    testId: "test1",
    candidateId: "2", // Jane Candidate
    candidateName: "Jane Candidate",
    score: 85,
    completedAt: "2023-07-01T14:30:00Z",
    status: "completed",
    feedback: "Excellent understanding of React and JavaScript. Could improve on CSS animations.",
  },
  {
    id: "sub2",
    testId: "test2",
    candidateId: "2", // Jane Candidate
    candidateName: "Jane Candidate",
    score: 0,
    completedAt: "",
    status: "scheduled",
  },
];

const mockReferrals: Referral[] = [
  {
    id: "ref1",
    jobId: "job1",
    candidateId: "2", // Jane Candidate
    providerId: "1", // John Provider
    testId: "test1",
    status: "test_completed",
    testScore: 85,
    referredOn: "2023-07-02",
    notes: "Strong candidate with excellent frontend skills.",
  },
  {
    id: "ref2",
    jobId: "job2",
    candidateId: "2", // Jane Candidate
    providerId: "1", // John Provider
    testId: "test2",
    status: "test_scheduled",
  },
];

const mockNotifications: Notification[] = [
  {
    id: "notif1",
    userId: "2", // Jane Candidate
    title: "Test Scheduled",
    message: "You have been scheduled for 'Backend Development Challenge' test.",
    type: "info",
    read: false,
    createdAt: "2023-06-30T10:00:00Z",
    link: "/tests",
  },
  {
    id: "notif2",
    userId: "1", // John Provider
    title: "Test Completed",
    message: "Jane Candidate has completed the Frontend Developer Skills Assessment with a score of 85%.",
    type: "success",
    read: false,
    createdAt: "2023-07-01T15:00:00Z",
    link: "/referrals",
  },
];

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [jobs, setJobs] = useState<Job[]>(mockJobs);
  const [tests, setTests] = useState<Test[]>(mockTests);
  const [testSubmissions, setTestSubmissions] = useState<TestSubmission[]>(mockTestSubmissions);
  const [referrals, setReferrals] = useState<Referral[]>(mockReferrals);
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);

  const addJob = (job: Omit<Job, "id" | "postedBy" | "postedDate" | "referrals">) => {
    const newJob: Job = {
      ...job,
      id: `job${jobs.length + 1}`,
      postedBy: "1", // Default to John Provider for demo
      postedDate: new Date().toISOString().split("T")[0],
      referrals: 0,
    };
    
    setJobs([...jobs, newJob]);
  };

  const addReferral = (referral: Omit<Referral, "id">) => {
    const newReferral: Referral = {
      ...referral,
      id: `ref${referrals.length + 1}`,
    };
    
    setReferrals([...referrals, newReferral]);
    
    // Add notification for candidate
    const newNotification: Notification = {
      id: `notif${notifications.length + 1}`,
      userId: referral.candidateId,
      title: "New Referral",
      message: `You have been referred for a new job. Please check your referrals.`,
      type: "info",
      read: false,
      createdAt: new Date().toISOString(),
      link: "/referrals",
    };
    
    setNotifications([...notifications, newNotification]);
  };

  const updateReferralStatus = (
    referralId: string,
    status: Referral["status"],
    data?: Partial<Referral>
  ) => {
    setReferrals(
      referrals.map((ref) =>
        ref.id === referralId
          ? { ...ref, status, ...data }
          : ref
      )
    );
    
    // Add relevant notification
    const referral = referrals.find((ref) => ref.id === referralId);
    if (referral) {
      const newNotification: Notification = {
        id: `notif${notifications.length + 1}`,
        userId: status === "referred" || status === "rejected" ? referral.candidateId : referral.providerId,
        title: getNotificationTitle(status),
        message: getNotificationMessage(status, referral),
        type: getNotificationType(status),
        read: false,
        createdAt: new Date().toISOString(),
        link: "/referrals",
      };
      
      setNotifications([...notifications, newNotification]);
    }
  };

  const scheduleTest = (testId: string, candidateId: string) => {
    const newSubmission: TestSubmission = {
      id: `sub${testSubmissions.length + 1}`,
      testId,
      candidateId,
      candidateName: "Jane Candidate", // Hardcoded for demo
      score: 0,
      completedAt: "",
      status: "scheduled",
    };
    
    setTestSubmissions([...testSubmissions, newSubmission]);
    
    // Find referrals related to this test and candidate
    const relatedReferrals = referrals.filter(
      (ref) => ref.testId === testId && ref.candidateId === candidateId
    );
    
    // Update referral statuses
    relatedReferrals.forEach((ref) => {
      updateReferralStatus(ref.id, "test_scheduled");
    });
    
    // Add notification
    const test = tests.find((t) => t.id === testId);
    if (test) {
      const newNotification: Notification = {
        id: `notif${notifications.length + 1}`,
        userId: candidateId,
        title: "Test Scheduled",
        message: `You have been scheduled for the "${test.title}" test.`,
        type: "info",
        read: false,
        createdAt: new Date().toISOString(),
        link: "/tests",
      };
      
      setNotifications([...notifications, newNotification]);
    }
  };

  const completeTest = (submissionId: string, score: number) => {
    setTestSubmissions(
      testSubmissions.map((sub) =>
        sub.id === submissionId
          ? {
              ...sub,
              score,
              completedAt: new Date().toISOString(),
              status: "completed",
            }
          : sub
      )
    );
    
    const submission = testSubmissions.find((sub) => sub.id === submissionId);
    if (submission) {
      // Find referrals related to this test and candidate
      const relatedReferrals = referrals.filter(
        (ref) =>
          ref.testId === submission.testId &&
          ref.candidateId === submission.candidateId
      );
      
      // Update referral statuses
      relatedReferrals.forEach((ref) => {
        updateReferralStatus(ref.id, "test_completed", { testScore: score });
      });
      
      // Add notifications
      const test = tests.find((t) => t.id === submission.testId);
      if (test) {
        // Notification for candidate
        const candidateNotification: Notification = {
          id: `notif${notifications.length + 1}`,
          userId: submission.candidateId,
          title: "Test Completed",
          message: `You have completed the "${test.title}" test with a score of ${score}%.`,
          type: "success",
          read: false,
          createdAt: new Date().toISOString(),
          link: "/tests",
        };
        
        setNotifications([...notifications, candidateNotification]);
        
        // Notification for providers
        relatedReferrals.forEach((ref) => {
          const providerNotification: Notification = {
            id: `notif${notifications.length + 2}`,
            userId: ref.providerId,
            title: "Candidate Test Completed",
            message: `${submission.candidateName} has completed the "${test.title}" test with a score of ${score}%.`,
            type: "info",
            read: false,
            createdAt: new Date().toISOString(),
            link: "/referrals",
          };
          
          setNotifications([...notifications, providerNotification]);
        });
      }
    }
  };

  // Helper functions for notifications
  const getNotificationTitle = (status: Referral["status"]): string => {
    switch (status) {
      case "pending":
        return "New Referral Request";
      case "test_scheduled":
        return "Test Scheduled";
      case "test_completed":
        return "Test Completed";
      case "referred":
        return "Referred to Company";
      case "rejected":
        return "Referral Not Proceeding";
      default:
        return "Referral Update";
    }
  };

  const getNotificationMessage = (status: Referral["status"], referral: Referral): string => {
    switch (status) {
      case "pending":
        return "You have a new referral request pending.";
      case "test_scheduled":
        return "A test has been scheduled for your referral candidate.";
      case "test_completed":
        return `Candidate has completed the test with a score of ${referral.testScore}%.`;
      case "referred":
        return "Congratulations! You have been referred to the company.";
      case "rejected":
        return "Your referral application will not be proceeding further.";
      default:
        return "There has been an update to your referral.";
    }
  };

  const getNotificationType = (status: Referral["status"]): Notification["type"] => {
    switch (status) {
      case "referred":
        return "success";
      case "rejected":
        return "error";
      default:
        return "info";
    }
  };

  return (
    <DataContext.Provider
      value={{
        jobs,
        tests,
        testSubmissions,
        referrals,
        notifications,
        addJob,
        addReferral,
        updateReferralStatus,
        scheduleTest,
        completeTest,
      }}
    >
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error("useData must be used within a DataProvider");
  }
  return context;
};
