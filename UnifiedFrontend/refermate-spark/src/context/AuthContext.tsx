
import React, { createContext, useContext, useState, useEffect } from "react";
import { toast } from "sonner";

export interface User {
  id: string;
  name: string;
  email: string;
  role: "provider" | "candidate" | "admin";
  avatar?: string;
  company?: string;
  position?: string;
  subscriptionTier?: "free" | "small" | "mid" | "top";
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  loginWithLinkedIn: () => Promise<void>;
  register: (
    email: string,
    password: string,
    name: string,
    role: "provider" | "candidate"
  ) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock user data
const mockUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>vide<PERSON>",
    email: "<EMAIL>",
    role: "provider",
    avatar: "https://i.pravatar.cc/150?img=1",
    company: "Tech Innovations Inc.",
    position: "Senior HR Manager",
    subscriptionTier: "top",
  },
  {
    id: "2",
    name: "<PERSON> Candidate",
    email: "<EMAIL>",
    role: "candidate",
    avatar: "https://i.pravatar.cc/150?img=2",
    subscriptionTier: "free",
  },
  {
    id: "3",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    avatar: "https://i.pravatar.cc/150?img=3",
    company: "Refermitra",
    position: "Platform Administrator",
    subscriptionTier: "top",
  },
];

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check for saved auth on mount
  useEffect(() => {
    const savedUser = localStorage.getItem("refermitra_user");
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error("Failed to parse saved user:", error);
        localStorage.removeItem("refermitra_user");
      }
    }
    // Simulate network delay
    setTimeout(() => setIsLoading(false), 1000);
  }, []);

  // Save user to localStorage when it changes
  useEffect(() => {
    if (user) {
      localStorage.setItem("refermitra_user", JSON.stringify(user));
    } else {
      localStorage.removeItem("refermitra_user");
    }
  }, [user]);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Simulate API request delay
      await new Promise((resolve) => setTimeout(resolve, 1500));
      
      const foundUser = mockUsers.find((u) => u.email === email);
      
      if (foundUser) {
        setUser(foundUser);
        toast.success(`Welcome back, ${foundUser.name}!`);
      } else {
        toast.error("Invalid email or password");
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error("An error occurred during login");
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    setIsLoading(true);
    
    try {
      // Simulate API request delay
      await new Promise((resolve) => setTimeout(resolve, 1500));
      
      // Return the provider for demo purposes
      const providerUser = mockUsers.find((u) => u.role === "provider");
      
      if (providerUser) {
        setUser(providerUser);
        toast.success(`Welcome back, ${providerUser.name}!`);
      }
    } catch (error) {
      console.error("Google login error:", error);
      toast.error("An error occurred during Google login");
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithLinkedIn = async () => {
    setIsLoading(true);
    
    try {
      // Simulate API request delay
      await new Promise((resolve) => setTimeout(resolve, 1500));
      
      // Return the candidate for demo purposes
      const candidateUser = mockUsers.find((u) => u.role === "candidate");
      
      if (candidateUser) {
        setUser(candidateUser);
        toast.success(`Welcome back, ${candidateUser.name}!`);
      }
    } catch (error) {
      console.error("LinkedIn login error:", error);
      toast.error("An error occurred during LinkedIn login");
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (
    email: string,
    password: string,
    name: string,
    role: "provider" | "candidate"
  ) => {
    setIsLoading(true);
    
    try {
      // Simulate API request delay
      await new Promise((resolve) => setTimeout(resolve, 1500));
      
      // Check if email already exists
      const existingUser = mockUsers.find((u) => u.email === email);
      
      if (existingUser) {
        toast.error("Email already in use");
        return;
      }
      
      const newUser: User = {
        id: Math.random().toString(36).substring(2, 15),
        name,
        email,
        role,
        avatar: `https://i.pravatar.cc/150?img=${Math.floor(Math.random() * 70)}`,
        subscriptionTier: "free",
      };
      
      setUser(newUser);
      toast.success("Registration successful!");
    } catch (error) {
      console.error("Registration error:", error);
      toast.error("An error occurred during registration");
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    toast.info("You have been logged out");
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        login,
        loginWithGoogle,
        loginWithLinkedIn,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
