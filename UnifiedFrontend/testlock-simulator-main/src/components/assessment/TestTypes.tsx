import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import CodeEditor from './CodeEditor';
import { Code, FileText, Database, GitBranch } from 'lucide-react';
import MCQTest from './MCQTest';
import SQLTest from './SQLTest';
import GitQuestion from './GitQuestion';
import SubjectiveQuestion from './SubjectiveQuestion';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';

const TestTypes = () => {
  const [currentTab, setCurrentTab] = useState('mcq');
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Set the current tab based on the URL path
    if (location.pathname.includes('/problem/')) {
      setCurrentTab('coding');
    }
  }, [location]);

  const handleTabChange = (value: string) => {
    setCurrentTab(value);
    if (value === 'coding' && !location.pathname.includes('/problem/')) {
      // Navigate to the first coding problem only if not already on a problem
      navigate('problem/1');
    }
  };

  return (
    <Tabs value={currentTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="mb-6 w-full justify-start">
        <TabsTrigger value="mcq" className="gap-2 data-[state=active]:bg-primary/10">
          <FileText className="h-4 w-4" />
          Multiple Choice
        </TabsTrigger>
        <TabsTrigger value="coding" className="gap-2 data-[state=active]:bg-primary/10">
          <Code className="h-4 w-4" />
          Coding
        </TabsTrigger>
        <TabsTrigger value="sql" className="gap-2 data-[state=active]:bg-primary/10">
          <Database className="h-4 w-4" />
          SQL
        </TabsTrigger>
        <TabsTrigger value="git" className="gap-2 data-[state=active]:bg-primary/10">
          <GitBranch className="h-4 w-4" />
          Git
        </TabsTrigger>
        <TabsTrigger value="subjective" className="gap-2 data-[state=active]:bg-primary/10">
          <FileText className="h-4 w-4" />
          Subjective
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="mcq" className="mt-0">
        <MCQTest />
      </TabsContent>
      
      <TabsContent value="coding" className="mt-0">
        <Outlet />
      </TabsContent>
      
      <TabsContent value="sql" className="mt-0">
        <SQLTest />
      </TabsContent>
      
      <TabsContent value="git" className="mt-0">
        <GitQuestion />
      </TabsContent>
      
      <TabsContent value="subjective" className="mt-0">
        <SubjectiveQuestion />
      </TabsContent>
    </Tabs>
  );
};

export default TestTypes;
