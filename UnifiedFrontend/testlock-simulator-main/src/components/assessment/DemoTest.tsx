import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import GlassCard from '../ui/GlassCard';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Check, ArrowRight } from 'lucide-react';
import { DemoState, DemoQuestion } from '@/types/test/DemoTest.types';
import { submitDemoTest } from '@/services/demoTest.service';

const DemoTest = () => {
  const navigate = useNavigate();
  const [state, setState] = useState<DemoState>({
    currentQuestion: 0,
    answers: ['', '', ''],
    submitted: false,
    isSubmitting: false
  });
  
  const questions: DemoQuestion[] = [
    {
      id: 1,
      question: "Which button should you click to submit your answers?",
      options: [
        { id: 'a', text: "The 'Next' button" },
        { id: 'b', text: "The 'Submit' button" },
        { id: 'c', text: "The 'Save' button" },
        { id: 'd', text: "The 'Finish' button" }
      ],
      correctAnswer: 'b'
    },
    {
      id: 2,
      question: "What happens if you try to navigate away from the assessment page?",
      options: [
        { id: 'a', text: "Nothing, you can freely navigate" },
        { id: 'b', text: "The assessment continues in the background" },
        { id: 'c', text: "You will receive a warning message" },
        { id: 'd', text: "The assessment automatically submits" }
      ],
      correctAnswer: 'c'
    },
    {
      id: 3,
      question: "What should you do if you experience technical issues during the test?",
      options: [
        { id: 'a', text: "Restart your computer immediately" },
        { id: 'b', text: "Use the 'Report Issue' button" },
        { id: 'c', text: "Wait until the end of the test" },
        { id: 'd', text: "Close the browser and start again" }
      ],
      correctAnswer: 'b'
    }
  ];
  
  const currentQuestionData = questions[state.currentQuestion];
  
  const handleAnswerChange = (value: string) => {
    setState(prev => ({
      ...prev,
      answers: prev.answers.map((answer, index) => 
        index === prev.currentQuestion ? value : answer
      )
    }));
  };
  
  const handleNext = () => {
    setState(prev => ({
      ...prev,
      currentQuestion: Math.min(prev.currentQuestion + 1, questions.length - 1)
    }));
  };
  
  const handlePrevious = () => {
    setState(prev => ({
      ...prev,
      currentQuestion: Math.max(prev.currentQuestion - 1, 0)
    }));
  };
  
  const handleSubmit = async () => {
    // Check if all questions are answered
    if (state.answers.some(answer => answer === '')) {
      toast.error('Please answer all questions before submitting');
      return;
    }
    
    setState(prev => ({ ...prev, isSubmitting: true }));
    
    try {
      const submission = await submitDemoTest('demo-123', state.answers);
      setState(prev => ({
        ...prev,
        submitted: true,
        isSubmitting: false,
        score: submission.score
      }));
      toast.success(`You scored ${submission.score.correct} out of ${submission.score.total}`);
    } catch (error) {
      setState(prev => ({ ...prev, isSubmitting: false }));
      toast.error('Failed to submit demo test. Please try again.');
    }
  };
  
  return (
    <div className="max-w-3xl mx-auto animate-fade-in">
      <h2 className="h2 mb-2 text-center">Demo Assessment</h2>
      <p className="text-muted-foreground mb-8 text-center max-w-xl mx-auto">
        This is a short demo to help you get familiar with the assessment interface.
        Try to answer the questions below to continue.
      </p>
      
      {!state.submitted ? (
        <GlassCard className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <span className="text-sm font-medium">Question {state.currentQuestion + 1} of {questions.length}</span>
            <div className="flex items-center gap-1">
              {questions.map((_, index) => (
                <div 
                  key={index}
                  className={`h-2 w-2 rounded-full ${
                    index === state.currentQuestion 
                      ? 'bg-primary' 
                      : state.answers[index] 
                        ? 'bg-primary/50' 
                        : 'bg-muted'
                  }`}
                ></div>
              ))}
            </div>
          </div>
          
          <div className="mb-8">
            <h3 className="text-xl font-semibold mb-4">{currentQuestionData.question}</h3>
            
            <RadioGroup 
              value={state.answers[state.currentQuestion]} 
              onValueChange={handleAnswerChange}
              className="space-y-3"
            >
              {currentQuestionData.options.map(option => (
                <div key={option.id} className="flex items-center space-x-2">
                  <RadioGroupItem 
                    value={option.id} 
                    id={`option-${option.id}`} 
                    className="focus-ring"
                  />
                  <Label 
                    htmlFor={`option-${option.id}`}
                    className="flex-1 py-2 cursor-pointer"
                  >
                    {option.text}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
          
          <div className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={handlePrevious}
              disabled={state.currentQuestion === 0}
              className="focus-ring"
            >
              Previous
            </Button>
            
            {state.currentQuestion < questions.length - 1 ? (
              <Button 
                onClick={handleNext} 
                disabled={!state.answers[state.currentQuestion]}
                className="focus-ring"
              >
                Next
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit}
                className="focus-ring btn-hover"
                disabled={state.isSubmitting}
              >
                {state.isSubmitting ? 'Submitting...' : 'Submit'}
              </Button>
            )}
          </div>
        </GlassCard>
      ) : (
        <div className="text-center animate-scale-in">
          <div className="flex items-center justify-center h-20 w-20 mx-auto rounded-full bg-green-100 mb-6">
            <Check className="h-10 w-10 text-green-500" />
          </div>
          <h3 className="h3 mb-2">Demo Completed!</h3>
          <p className="text-muted-foreground mb-8 max-w-md mx-auto">
            You've completed the demo assessment. You're now ready to begin your real assessment.
          </p>
          <Button 
            onClick={() => navigate('/assessment')}
            size="lg"
            className="gap-2 btn-hover"
          >
            Start Assessment
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default DemoTest;
