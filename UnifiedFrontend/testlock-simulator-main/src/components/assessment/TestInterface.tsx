
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TestTypes from './TestTypes';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Clock, AlertTriangle, CheckCircle2 } from 'lucide-react';

const TestInterface = () => {
  const navigate = useNavigate();
  const [timeRemaining, setTimeRemaining] = useState(60 * 60); // 60 minutes in seconds
  const [isConfirmEndOpen, setIsConfirmEndOpen] = useState(false);
  const [isFinished, setIsFinished] = useState(false);
  
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 0) {
          clearInterval(timer);
          handleTestEnd();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  const getTimeColor = () => {
    if (timeRemaining <= 300) return 'text-red-500'; // Less than 5 minutes
    if (timeRemaining <= 600) return 'text-amber-500'; // Less than 10 minutes
    return 'text-muted-foreground';
  };
  
  const handleEndTest = () => {
    setIsConfirmEndOpen(true);
  };
  
  const handleTestEnd = () => {
    setIsFinished(true);
    toast.success('Your test has been submitted successfully');
  };
  
  if (isFinished) {
    return (
      <div className="max-w-md mx-auto py-16 px-4 text-center animate-scale-in">
        <div className="flex items-center justify-center h-20 w-20 mx-auto rounded-full bg-green-100 mb-6">
          <CheckCircle2 className="h-10 w-10 text-green-500" />
        </div>
        <h2 className="h2 mb-2">Assessment Completed!</h2>
        <p className="text-muted-foreground mb-8">
          Thank you for completing this assessment. Your answers have been submitted and will be reviewed.
        </p>
        <Button onClick={() => navigate('/')} className="btn-hover">
          Return to Home
        </Button>
      </div>
    );
  }
  
  return (
    <>
      <div className="flex items-center justify-between mb-6 sticky top-16 z-10 bg-background/95 backdrop-blur-sm py-4">
        <h2 className="h3">JavaScript Assessment</h2>
        <div className="flex items-center gap-6">
          <div className={`flex items-center gap-2 ${getTimeColor()}`}>
            <Clock className="h-4 w-4" />
            <span className="font-mono">{formatTime(timeRemaining)}</span>
          </div>
          
          <Button onClick={handleEndTest} variant="outline" className="focus-ring">
            End Test
          </Button>
        </div>
      </div>
      
      <TestTypes />
      
      <Dialog open={isConfirmEndOpen} onOpenChange={setIsConfirmEndOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>End Test Confirmation</DialogTitle>
          </DialogHeader>
          
          <div className="py-4 flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="mb-2">Are you sure you want to end this test?</p>
              <p className="text-sm text-muted-foreground">
                This action cannot be undone. Your test will be submitted with your current answers.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsConfirmEndOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={() => {
                setIsConfirmEndOpen(false);
                handleTestEnd();
              }}
            >
              Yes, End Test
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TestInterface;
