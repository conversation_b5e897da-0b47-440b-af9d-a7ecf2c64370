import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { AlertTriangle, MessageSquare, Flag, Info } from 'lucide-react';
import { ProctorControlsProps, ProctorControlsState, Issue } from '@/types/proctor/ProctorControls.types';
import { submitIssueReport } from '@/services/proctorControls.service';

const ProctorControls = ({ isFullScreen, onRequestFullScreen }: ProctorControlsProps) => {
  const [state, setState] = useState<ProctorControlsState>({
    issueDialogOpen: false,
    selectedIssue: null,
    issueDescription: '',
    isSubmitting: false
  });
  
  const issues: Issue[] = [
    { id: 'technical', label: 'Technical Problem', icon: <AlertTriangle className="h-4 w-4" /> },
    { id: 'question', label: 'Question Clarification', icon: <MessageSquare className="h-4 w-4" /> },
    { id: 'other', label: 'Other Issue', icon: <Flag className="h-4 w-4" /> },
  ];
  
  const handleIssueSubmit = async () => {
    if (!state.selectedIssue) {
      toast.error('Please select an issue type');
      return;
    }
    
    setState(prev => ({ ...prev, isSubmitting: true }));
    
    try {
      const report = await submitIssueReport({
        type: state.selectedIssue,
        description: state.issueDescription
      });
      
      setState(prev => ({
        ...prev,
        lastReportedIssue: report,
        issueDialogOpen: false,
        selectedIssue: null,
        issueDescription: '',
        isSubmitting: false
      }));
      
      toast.success('Your issue has been reported');
    } catch (error) {
      setState(prev => ({ ...prev, isSubmitting: false }));
      toast.error('Failed to submit issue report. Please try again.');
    }
  };
  
  return (
    <>
      <div className="fixed bottom-6 right-6 z-40 flex gap-3">
        {!isFullScreen && (
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={onRequestFullScreen}
            className="shadow-lg animate-pulse-soft"
          >
            Return to Full Screen
          </Button>
        )}
        
        <Button 
          variant="outline" 
          size="sm" 
          className="bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white"
          onClick={() => setState(prev => ({ ...prev, issueDialogOpen: true }))}
        >
          <Flag className="h-4 w-4 mr-2" />
          Report Issue
        </Button>
      </div>
      
      <Dialog open={state.issueDialogOpen} onOpenChange={(open) => setState(prev => ({ ...prev, issueDialogOpen: open }))}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Report an Issue</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-1 gap-3">
              {issues.map((issue) => (
                <Button
                  key={issue.id}
                  variant="outline"
                  className={`justify-start h-auto py-3 px-4 ${
                    state.selectedIssue === issue.id ? 'border-primary bg-primary/5' : ''
                  }`}
                  onClick={() => setState(prev => ({ ...prev, selectedIssue: issue.id }))}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${
                      state.selectedIssue === issue.id 
                        ? 'bg-primary/10 text-primary' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      {issue.icon}
                    </div>
                    <span>{issue.label}</span>
                  </div>
                </Button>
              ))}
            </div>
            
            <div className="space-y-2">
              <label htmlFor="issue-description" className="text-sm font-medium">
                Describe your issue
              </label>
              <textarea
                id="issue-description"
                rows={3}
                value={state.issueDescription}
                onChange={(e) => setState(prev => ({ ...prev, issueDescription: e.target.value }))}
                className="w-full p-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50"
                placeholder="Please provide details about your issue..."
              />
            </div>
            
            <div className="flex items-center gap-2 p-3 bg-amber-50 text-amber-800 rounded-md text-sm">
              <Info className="h-4 w-4 flex-shrink-0" />
              <p>Your report will be sent to the proctor. Continue with your assessment while waiting for a response.</p>
            </div>
          </div>
          
          <DialogFooter className="sm:justify-between">
            <Button 
              variant="outline" 
              onClick={() => setState(prev => ({ ...prev, issueDialogOpen: false }))}
            >
              Cancel
            </Button>
            <Button
              onClick={handleIssueSubmit}
              className="btn-hover"
              disabled={state.isSubmitting}
            >
              {state.isSubmitting ? 'Submitting...' : 'Submit Report'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ProctorControls;
