import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import GlassCard from '../ui/GlassCard';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Check, X, Camera, Mic, Monitor, Cpu } from 'lucide-react';
import { toast } from 'sonner';
import { SystemChecks, SystemCheckState } from '@/types/system/SystemCheck.types';
import { performSystemCheck, checkCameraAccess, checkMicrophoneAccess } from '@/services/systemCheck.service';

const SystemCheck = () => {
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [state, setState] = useState<SystemCheckState>({
    progress: 0,
    currentCheck: 'initializing',
    checks: {
      browser: { status: 'pending', name: 'Browser Compatibility' },
      camera: { status: 'pending', name: 'Camera Access' },
      microphone: { status: 'pending', name: 'Microphone Access' },
      network: { status: 'pending', name: 'Network Connectivity' },
      system: { status: 'pending', name: 'System Performance' }
    },
    isComplete: false
  });

  useEffect(() => {
    const runChecks = async () => {
      try {
        // Check browser compatibility
        setState(prev => ({ ...prev, currentCheck: 'browser' }));
        await updateCheckStatus('browser', true);
        
        // Check camera
        setState(prev => ({ ...prev, currentCheck: 'camera' }));
        const cameraAccess = await checkCameraAccess();
        await updateCheckStatus('camera', cameraAccess);
        
        // Check microphone
        setState(prev => ({ ...prev, currentCheck: 'microphone' }));
        const micAccess = await checkMicrophoneAccess();
        await updateCheckStatus('microphone', micAccess);
        
        // Check network and system
        setState(prev => ({ ...prev, currentCheck: 'network' }));
        const systemCheckResult = await performSystemCheck();
        
        // Update network and system checks based on API response
        await updateCheckStatus('network', systemCheckResult.checks.network.status === 'success');
        await updateCheckStatus('system', systemCheckResult.checks.system.status === 'success');
        
        // Complete
        setState(prev => ({ ...prev, currentCheck: 'complete', isComplete: true }));
      } catch (error) {
        console.error('System check failed:', error);
        toast.error('System check failed. Please try again.');
      }
    };
    
    runChecks();
    
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  const updateCheckStatus = async (check: keyof SystemChecks, success: boolean) => {
    return new Promise<void>(resolve => {
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          checks: {
            ...prev.checks,
            [check]: { ...prev.checks[check], status: success ? 'success' : 'failed' }
          },
          progress: calculateProgress(prev.checks, check, success)
        }));
        resolve();
      }, 1000);
    });
  };
  
  const calculateProgress = (checks: SystemChecks, currentCheck: keyof SystemChecks, success: boolean) => {
    const updatedChecks = { ...checks, [currentCheck]: { ...checks[currentCheck], status: success ? 'success' : 'failed' } };
    const completedChecks = Object.values(updatedChecks).filter(check => 
      check.status === 'success' || check.status === 'failed'
    ).length;
    return Math.floor((completedChecks / Object.keys(checks).length) * 100);
  };
  
  const allChecksPassed = Object.values(state.checks).every(check => check.status === 'success');
  
  return (
    <div className="max-w-2xl mx-auto animate-fade-in">
      <h2 className="h2 mb-8 text-center">System Compatibility Check</h2>
      
      <GlassCard className="mb-8">
        <div className="mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium">System Check Progress</span>
            <span className="text-sm font-medium">{state.progress}%</span>
          </div>
          <Progress value={state.progress} className="h-2" />
        </div>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            {Object.entries(state.checks).map(([key, check]) => (
              <div key={key} className="flex items-center gap-3">
                {check.status === 'pending' && (
                  <div className={`h-5 w-5 rounded-full ${state.currentCheck === key ? 'bg-primary/20 animate-pulse' : 'bg-muted'}`}></div>
                )}
                {check.status === 'success' && (
                  <Check className="h-5 w-5 text-green-500" />
                )}
                {check.status === 'failed' && (
                  <X className="h-5 w-5 text-red-500" />
                )}
                <span className={`text-sm font-medium ${state.currentCheck === key ? 'text-primary' : ''}`}>
                  {check.name}
                </span>
              </div>
            ))}
          </div>
          
          <div className="bg-black/5 rounded-lg aspect-video overflow-hidden relative">
            {state.checks.camera.status !== 'success' ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <Camera className="h-10 w-10 text-muted-foreground/50" />
              </div>
            ) : null}
            <video 
              ref={videoRef} 
              autoPlay 
              playsInline 
              muted
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </GlassCard>
      
      {state.isComplete && (
        <div className="flex flex-col items-center">
          {allChecksPassed ? (
            <>
              <div className="flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <Check className="h-8 w-8 text-green-500" />
              </div>
              <h3 className="h3 mb-2">All Checks Passed!</h3>
              <p className="text-muted-foreground mb-6 text-center">
                Your system meets all the requirements needed for the assessment.
              </p>
              <Button onClick={() => navigate('/demo-test')} className="btn-hover">
                Continue to Demo Test
              </Button>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center h-16 w-16 rounded-full bg-amber-100 mb-4">
                <X className="h-8 w-8 text-amber-500" />
              </div>
              <h3 className="h3 mb-2">Some Checks Failed</h3>
              <p className="text-muted-foreground mb-6 text-center">
                Please fix the issues above to ensure a smooth assessment experience.
              </p>
              <div className="flex gap-4">
                <Button variant="outline" onClick={() => window.location.reload()}>
                  Try Again
                </Button>
                <Button 
                  onClick={() => navigate('/demo-test')} 
                  className="btn-hover"
                >
                  Continue Anyway
                </Button>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SystemCheck;
