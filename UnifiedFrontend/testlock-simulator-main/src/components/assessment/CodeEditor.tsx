import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Code, File, Maximize2, Minimize2 } from 'lucide-react';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import Editor, { OnMount } from '@monaco-editor/react';
import * as monaco from 'monaco-editor';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { useParams } from 'react-router-dom';
import { 
  executeCode, 
  submitSolution, 
  getProblem,
  getAllProblems,
  getLanguageBoilerplates
} from '@/services/codeEditor.service';
import { Problem, LanguageBoilerplate } from '@/types/code/CodeEditor.types';

const LANGUAGE_CONFIGS = {
  python: { 
    language: 'python',
    theme: 'vs-dark'
  },
  javascript: { 
    language: 'javascript',
    theme: 'vs-dark'
  },
  java: { 
    language: 'java',
    theme: 'vs-dark'
  }
};

const CodeEditor = () => {
  const { problemId } = useParams<{ problemId: string }>();
  const [problem, setProblem] = useState<Problem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [language, setLanguage] = useState<string>('javascript');
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [isCodeEditorFullScreen, setIsCodeEditorFullScreen] = useState(false);
  const [languageBoilerplates, setLanguageBoilerplates] = useState<LanguageBoilerplate[]>([]);
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const codeEditorContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchLanguageBoilerplates = async () => {
      try {
        const boilerplates = await getLanguageBoilerplates();
        setLanguageBoilerplates(boilerplates);
        if (boilerplates.length > 0) {
          setLanguage(boilerplates[0].language);
        }
      } catch (error) {
        console.error('Error fetching language boilerplates:', error);
        toast.error('Failed to load language templates');
      }
    };

    fetchLanguageBoilerplates();
  }, []);

  useEffect(() => {
    const fetchProblem = async () => {
      if (!problemId) {
        setError('No problem ID provided');
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        const problemData = await getProblem(problemId);
        setProblem(problemData);
        
        // Find the boilerplate for the current language
        const currentBoilerplate = languageBoilerplates.find(bp => bp.language === language);
        setCode(currentBoilerplate?.template || problemData.boilerplate[language] || '');
      } catch (error) {
        console.error('Error fetching problem:', error);
        setError('Failed to load problem. Please try again later.');
        toast.error('Failed to load problem');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProblem();
  }, [problemId, language, languageBoilerplates]);

  const toggleCodeEditorFullScreen = useCallback(() => {
    setIsCodeEditorFullScreen(!isCodeEditorFullScreen);
  }, [isCodeEditorFullScreen]);

  const handleLanguageChange = (selectedLanguage: keyof typeof LANGUAGE_CONFIGS) => {
    setLanguage(selectedLanguage);
    if (problem?.boilerplate[selectedLanguage]) {
      setCode(problem.boilerplate[selectedLanguage]);
    }
    
    if (editorRef.current) {
      const model = editorRef.current.getModel();
      if (model) {
        monaco.editor.setModelLanguage(model, LANGUAGE_CONFIGS[selectedLanguage].language);
      }
    }
  };

  const handleEditorDidMount: OnMount = (editor) => {
    editorRef.current = editor;
  };

  const runCode = async () => {
    if (!editorRef.current) return;

    setIsRunning(true);
    setOutput('');

    try {
      const code = editorRef.current.getValue();
      const result = await executeCode(language, code);
      console.log(result);
      
      setOutput(result.output);
      if (result.success) {
        toast.success('Code executed successfully!');
      } else {
        toast.error('Execution error');
      }
    } catch (error) {
      setOutput(`Error: ${error instanceof Error ? error.message : String(error)}`);
      toast.error('Execution error');
    } finally {
      setIsRunning(false);
    }
  };

  const handleSubmitSolution = async () => {
    if (!editorRef.current || !problemId) return;

    setIsRunning(true);
    setOutput('');

    try {
      const code = editorRef.current.getValue();
      const submission = {
        problemId,
        language,
        code,
        userId: 'current-user-id', // This should come from auth context
        timestamp: new Date().toISOString()
      };

      const result = await submitSolution(submission);
      
      // Format test results for display
      const formattedOutput = result.testResults.map((test, index) => 
        `Test Case ${index + 1}: ${test.passed ? '✓' : '✗'}\n` +
        `Input: ${test.input}\n` +
        `Expected: ${test.expectedOutput}\n` +
        `Actual: ${test.actualOutput}\n`
      ).join('\n');

      setOutput(formattedOutput);

      if (result.success && result.passedTestCases === result.totalTestCases) {
        toast.success('All test cases passed!');
      } else {
        toast.error(`Passed ${result.passedTestCases}/${result.totalTestCases} test cases`);
      }
    } catch (error) {
      setOutput(`Error: ${error instanceof Error ? error.message : String(error)}`);
      toast.error('Submission failed');
    } finally {
      setIsRunning(false);
    }
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-center space-y-4">
          <p className="text-red-500">{error}</p>
          <Button 
            onClick={() => window.location.href = '/assessment'}
            variant="outline"
          >
            Back to Assessment
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading || !problem) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <div className="h-6 w-6 border-2 border-t-transparent border-primary rounded-full animate-spin mx-auto"></div>
          <p className="text-muted-foreground">Loading problem...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full p-4">
      <div className={`grid gap-4 h-full ${isCodeEditorFullScreen ? 'grid-cols-1' : 'grid-cols-2'}`}>
        {!isCodeEditorFullScreen && (
          <div className="h-full overflow-auto bg-card/30 rounded-lg p-4 shadow-sm">
            <Tabs defaultValue="problem">
              <TabsList className="w-full mb-4">
                <TabsTrigger value="problem" className="w-full">Problem Description</TabsTrigger>
                <TabsTrigger value="approach" className="w-full">Approach</TabsTrigger>
              </TabsList>
              <div>
                <TabsContent value="problem" className="space-y-4">
                  <div className="bg-background/50 p-4 rounded-lg border border-border/50">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-xl font-bold text-foreground">
                        {problem?.title}
                        <span className="ml-2 text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded">
                          {problem?.difficulty}
                        </span>
                      </h3>
                    </div>
                    <p className="text-muted-foreground mb-4">{problem.description}</p>
                    
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2 text-foreground">Examples:</h4>
                        {problem.examples.map((example, index) => (
                          <div 
                            key={index} 
                            className="bg-secondary/30 p-3 rounded-md mb-2 border border-border/50"
                          >
                            <p><strong>Input:</strong> <code>{example.input}</code></p>
                            <p><strong>Output:</strong> <code>{example.output}</code></p>
                            {example.explanation && (
                              <p className="text-muted-foreground">{example.explanation}</p>
                            )}
                          </div>
                        ))}
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2 text-foreground">Constraints:</h4>
                        <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                          {problem.constraints.map((constraint, index) => (
                            <li key={index} className="text-sm">{constraint}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="approach">
                  <div className="bg-background/50 p-4 rounded-lg border border-border/50 space-y-2 text-sm">
                    <h3 className="font-semibold text-foreground">Optimal Approach</h3>
                    <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                      <li>Understand the problem requirements and constraints</li>
                      <li>Identify the optimal data structure and algorithm</li>
                      <li>Implement the solution with proper error handling</li>
                      <li>Test with edge cases and optimize if needed</li>
                    </ol>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        )}
        
        <div 
          ref={codeEditorContainerRef}
          className={`flex flex-col h-full overflow-hidden ${isCodeEditorFullScreen ? 'fixed inset-0 z-50 bg-background p-8' : ''}`}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              <span className="text-sm font-medium">Code Editor</span>
            </div>
            <div className="flex items-center mt-1">
            <Select value={language} onValueChange={(value: keyof typeof LANGUAGE_CONFIGS) => handleLanguageChange(value)}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Select Language" />
                        </SelectTrigger>
                        <SelectContent>
                          {languageBoilerplates.map((bp) => (
                            <SelectItem key={bp.language} value={bp.language}>
                              {bp.displayName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="icon" 
                onClick={toggleCodeEditorFullScreen}
                className="hover:bg-accent"
              >
                {isCodeEditorFullScreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 mb-4 bg-[#1e1e1e] rounded-lg overflow-hidden">
              <Editor
                height="100%"
                language={LANGUAGE_CONFIGS[language].language}
                theme={LANGUAGE_CONFIGS[language].theme}
                value={code}
                onMount={handleEditorDidMount}
                onChange={(value) => setCode(value || '')}
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  automaticLayout: true,
                }}
              />
            </div>
            
            <div className="h-1/3 bg-[#1e1e1e] rounded-lg overflow-hidden">
              <div className="flex items-center justify-between bg-[#2d2d2d] px-4 py-2">
                <div className="flex items-center gap-2">
                  <File className="h-4 w-4 text-white/70" />
                  <span className="text-xs text-white/70">Console Output</span>
                </div>
                <div className="flex gap-2">
                  <Button 
                    onClick={runCode} 
                    disabled={isRunning}
                    className="gap-1"
                  >
                    <Code className="h-3.5 w-3.5" />
                    Run
                  </Button>
                  <Button
                    onClick={handleSubmitSolution}
                    disabled={isRunning}
                    className="gap-1 btn-hover"
                  >
                    <File className="h-3.5 w-3.5" />
                    Submit
                  </Button>
                </div>
                {isRunning && (
                  <div className="text-xs text-white/70 flex items-center gap-1">
                    <div className="h-2 w-2 rounded-full bg-green-400 animate-pulse"></div>
                    Running...
                  </div>
                )}
              </div>
              <pre className="p-4 text-white/90 font-mono text-sm h-[calc(100%-36px)] overflow-auto">
                {output || '// Run your code to see output here'}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;
