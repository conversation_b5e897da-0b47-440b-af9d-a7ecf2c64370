
import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface NavbarProps {
  className?: string;
  isTestMode?: boolean;
}

const Navbar = ({ className, isTestMode = false }: NavbarProps) => {
  if (isTestMode) {
    return (
      <header className={cn('fixed top-0 inset-x-0 z-50 h-14 border-b border-border/40 bg-background/80 backdrop-blur-lg', className)}>
        <div className="container h-full flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-semibold text-sm">A</span>
            </div>
            <span className="font-medium">Assessment Portal</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-7 w-7 rounded-full bg-red-500 animate-pulse-soft"></div>
            <span className="text-sm font-medium">Recording</span>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className={cn('fixed top-0 inset-x-0 z-50 h-16 border-b border-border/40 bg-background/80 backdrop-blur-lg', className)}>
      <div className="container h-full flex items-center justify-between">
        <Link to="/" className="flex items-center gap-2 focus-ring rounded-lg">
          <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-semibold text-sm">A</span>
          </div>
          <span className="font-medium">Assessment Portal</span>
        </Link>
        <nav className="hidden md:flex items-center gap-6">
          <Link to="/" className="text-sm font-medium hover:text-primary focus-ring rounded-lg">Home</Link>
          <Link to="/login" className="text-sm font-medium hover:text-primary focus-ring rounded-lg">Login</Link>
        </nav>
      </div>
    </header>
  );
};

export default Navbar;
