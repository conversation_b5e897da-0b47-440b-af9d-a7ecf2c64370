
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import GlassCard from '../ui/GlassCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

interface AuthFormProps {
  type: 'login' | 'activate';
}

const AuthForm = ({ type }: AuthFormProps) => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [accessCode, setAccessCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Simple validation
    if (type === 'login' && (!email || !accessCode)) {
      toast.error('Please enter both email and access code');
      setLoading(false);
      return;
    }
    
    if (type === 'activate' && !accessCode) {
      toast.error('Please enter your access code');
      setLoading(false);
      return;
    }

    // Simulate API request to validate credentials
    setTimeout(() => {
      setLoading(false);
      
      // In a real application, this would be a backend validation
      // For demo, we'll consider specific formats as "valid"
      
      // Simple validation criteria for demo
      const isValidEmail = email.includes('@') && email.includes('.');
      const isValidAccessCode = accessCode.length >= 6;
      
      if (type === 'login') {
        if (isValidEmail && isValidAccessCode) {
          // Store user info in session storage for verification later
          sessionStorage.setItem('userEmail', email);
          sessionStorage.setItem('accessCode', accessCode);
          sessionStorage.setItem('isAuthenticated', 'true');
          
          toast.success('Login successful');
          navigate('/verify-identity');
        } else {
          toast.error('Invalid email or access code');
        }
      } else {
        if (isValidAccessCode) {
          // For activation, we just need a valid access code
          sessionStorage.setItem('accessCode', accessCode);
          sessionStorage.setItem('isAuthenticated', 'true');
          
          toast.success('Assessment link activated');
          navigate('/verify-identity');
        } else {
          toast.error('Invalid access code');
        }
      }
    }, 1500);
  };

  return (
    <GlassCard className="w-full max-w-md mx-auto animate-scale-in">
      <h2 className="h3 mb-6 text-center">
        {type === 'login' ? 'Login to your Assessment' : 'Activate your Assessment'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {type === 'login' && (
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email Address
            </label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="focus-ring"
              required
            />
          </div>
        )}
        
        <div className="space-y-2">
          <label htmlFor="accessCode" className="text-sm font-medium">
            Access Code
          </label>
          <Input
            id="accessCode"
            type="text"
            value={accessCode}
            onChange={(e) => setAccessCode(e.target.value)}
            placeholder="Enter your access code"
            className="focus-ring"
            required
          />
        </div>
        
        <Button 
          type="submit" 
          className="w-full mt-6 btn-hover"
          disabled={loading}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            type === 'login' ? 'Login' : 'Start Assessment'
          )}
        </Button>
      </form>
    </GlassCard>
  );
};

export default AuthForm;
