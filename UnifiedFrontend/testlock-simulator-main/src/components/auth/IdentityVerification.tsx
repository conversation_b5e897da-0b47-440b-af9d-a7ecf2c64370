import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import GlassCard from '../ui/GlassCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Check, Camera } from 'lucide-react';
import { UserDetails, VerificationState } from '@/types/identity/IdentityVerification.types';
import { verifyIdentity, setupWebSocket } from '@/services/identityVerification.service';

const IdentityVerification = () => {
  const navigate = useNavigate();
  const [verificationState, setVerificationState] = useState<VerificationState>({
    step: 1,
    loading: false,
    error: null,
    verificationStatus: 'idle',
    verificationId: null
  });
  const [userDetails, setUserDetails] = useState<UserDetails>({
    fullName: '',
    idNumber: '',
    photoTaken: false
  });
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserDetails(prev => ({ ...prev, [name]: value }));
  };
  
  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ video: true });
      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please check permissions and try again.');
    }
  };
  
  const takePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const context = canvasRef.current.getContext('2d');
      if (context) {
        canvasRef.current.width = videoRef.current.videoWidth;
        canvasRef.current.height = videoRef.current.videoHeight;
        context.drawImage(videoRef.current, 0, 0);
        const photoData = canvasRef.current.toDataURL('image/jpeg');
        setUserDetails(prev => ({ ...prev, photoTaken: true, photoData }));
        toast.success('Identity photo captured');
      }
    }
  };
  
  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  useEffect(() => {
    if (verificationState.verificationId) {
      const cleanup = setupWebSocket(verificationState.verificationId, (status) => {
        if (status.status === 'verified') {
          setVerificationState(prev => ({ ...prev, verificationStatus: 'completed' }));
          toast.success('Identity verification successful');
          navigate('/system-check');
        } else if (status.status === 'rejected') {
          setVerificationState(prev => ({ ...prev, verificationStatus: 'failed', error: status.message }));
          toast.error(status.message || 'Verification failed');
        }
      });

      return cleanup;
    }
  }, [verificationState.verificationId, navigate]);
  
  const handleNextStep = async () => {
    if (verificationState.step === 1) {
      if (!userDetails.fullName || !userDetails.idNumber) {
        toast.error('Please fill in all required fields');
        return;
      }
      startCamera();
      setVerificationState(prev => ({ ...prev, step: 2 }));
    } else if (verificationState.step === 2) {
      if (!userDetails.photoTaken || !userDetails.photoData) {
        toast.error('Please take a photo for identity verification');
        return;
      }
      
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      
      setVerificationState(prev => ({ ...prev, loading: true, verificationStatus: 'processing' }));
      
      try {
        const payload = {
          fullName: userDetails.fullName,
          idNumber: userDetails.idNumber,
          photoData: userDetails.photoData,
          timestamp: new Date().toISOString(),
          metadata: {
            deviceInfo: navigator.userAgent
          }
        };

        const response = await verifyIdentity(payload);
        setVerificationState(prev => ({ 
          ...prev, 
          verificationId: response.verificationId,
          loading: false
        }));
      } catch (error) {
        setVerificationState(prev => ({ 
          ...prev, 
          loading: false,
          error: error instanceof Error ? error.message : 'Verification failed',
          verificationStatus: 'failed'
        }));
        toast.error('Verification failed. Please try again.');
      }
    }
  };
  
  return (
    <GlassCard className="w-full max-w-md mx-auto animate-scale-in">
      <h2 className="h3 mb-6 text-center">Identity Verification</h2>
      
      {verificationState.step === 1 ? (
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground mb-4">
            Please provide your identification details to verify your identity before proceeding with the assessment.
          </p>
          
          <div className="space-y-2">
            <label htmlFor="fullName" className="text-sm font-medium">
              Full Name
            </label>
            <Input
              id="fullName"
              name="fullName"
              value={userDetails.fullName}
              onChange={handleInputChange}
              placeholder="Enter your full name"
              className="focus-ring"
              required
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="idNumber" className="text-sm font-medium">
              ID Number / Registration Number
            </label>
            <Input
              id="idNumber"
              name="idNumber"
              value={userDetails.idNumber}
              onChange={handleInputChange}
              placeholder="Enter your ID or registration number"
              className="focus-ring"
              required
            />
          </div>
          
          <Button 
            onClick={handleNextStep} 
            className="w-full mt-6 btn-hover"
          >
            Next
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground mb-4">
            Please take a photo for identity verification. Ensure your face is clearly visible.
          </p>
          
          <div className="relative border-2 border-dashed border-muted-foreground/30 rounded-lg overflow-hidden bg-black/5 aspect-video mb-4">
            {!userDetails.photoTaken ? (
              <video 
                ref={videoRef}
                autoPlay 
                muted 
                playsInline
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
                <div className="bg-green-500/10 rounded-full p-3">
                  <Check className="h-8 w-8 text-green-500" />
                </div>
              </div>
            )}
            <canvas ref={canvasRef} className="hidden" />
          </div>
          
          <div className="flex gap-4">
            {!userDetails.photoTaken ? (
              <Button 
                onClick={takePhoto} 
                className="flex-1 gap-2"
                variant="outline"
              >
                <Camera className="h-4 w-4" />
                Take Photo
              </Button>
            ) : (
              <Button 
                onClick={() => setUserDetails(prev => ({ ...prev, photoTaken: false, photoData: undefined }))} 
                className="flex-1"
                variant="outline"
              >
                Retake Photo
              </Button>
            )}
            
            <Button 
              onClick={handleNextStep} 
              className="flex-1 btn-hover"
              disabled={verificationState.loading}
            >
              {verificationState.loading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </span>
              ) : (
                'Verify & Continue'
              )}
            </Button>
          </div>
        </div>
      )}
    </GlassCard>
  );
};

export default IdentityVerification;
