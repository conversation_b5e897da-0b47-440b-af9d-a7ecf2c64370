
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface AuthGuardProps {
  children: React.ReactNode;
  requiredAuth?: boolean;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  requiredAuth = true 
}) => {
  const navigate = useNavigate();
  
  useEffect(() => {
    const isAuthenticated = sessionStorage.getItem('isAuthenticated') === 'true';
    
    if (requiredAuth && !isAuthenticated) {
      toast.error('Please login to access this page');
      navigate('/login');
    }
  }, [navigate, requiredAuth]);
  
  return <>{children}</>;
};

export default AuthGuard;
