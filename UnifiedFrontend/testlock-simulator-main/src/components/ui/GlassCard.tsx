
import React from 'react';
import { cn } from '@/lib/utils';

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'dark';
  hoverable?: boolean;
}

const GlassCard = ({
  className,
  children,
  variant = 'default',
  hoverable = false,
  ...props
}: GlassCardProps) => {
  return (
    <div
      className={cn(
        'rounded-xl p-6',
        variant === 'default' ? 'glass' : 'glass-dark',
        hoverable && 'card-hover',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default GlassCard;
