
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 221 83% 96%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased overflow-x-hidden;
  }

  /* Glass effect styles */
  .glass {
    @apply bg-white/60 backdrop-blur-lg border border-white/20 shadow-sm;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-lg border border-white/10 shadow-sm;
  }

  /* Smooth transitions for all interactive elements */
  a, button, input, select, textarea {
    @apply transition-all duration-200;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/30;
  }

  /* Shimmer effect */
  .shimmer {
    @apply bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:200%_100%] animate-shimmer;
  }
}

@layer components {
  .h1 {
    @apply text-4xl font-bold tracking-tight sm:text-5xl;
  }

  .h2 {
    @apply text-3xl font-semibold tracking-tight;
  }

  .h3 {
    @apply text-2xl font-semibold;
  }

  .h4 {
    @apply text-xl font-semibold;
  }

  /* Button hover animation */
  .btn-hover {
    @apply relative overflow-hidden;
  }

  .btn-hover::after {
    @apply content-[''] absolute inset-0 w-full h-full bg-white/20 scale-x-0 transition-transform duration-300 origin-left;
  }

  .btn-hover:hover::after {
    @apply scale-x-100;
  }

  /* Card hover effect */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
