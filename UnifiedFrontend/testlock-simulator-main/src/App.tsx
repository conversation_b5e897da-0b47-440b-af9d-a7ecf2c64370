import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Login from "./pages/Login";
import VerifyIdentity from "./pages/VerifyIdentity";
import SystemCheck from "./pages/SystemCheck";
import DemoTest from "./pages/DemoTest";
import Assessment from "./pages/Assessment";
import NotFound from "./pages/NotFound";
import CodeEditor from "./components/assessment/CodeEditor";

const queryClient = new QueryClient();

const App = () => {
  return (
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/verify-identity" element={<VerifyIdentity />} />
              <Route path="/system-check" element={<SystemCheck />} />
              <Route path="/demo-test" element={<DemoTest />} />
              <Route path="/assessment" element={<Assessment />}>
                <Route path="problem/:problemId" element={<CodeEditor />} />
              </Route>
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </React.StrictMode>
  );
};

export default App;
