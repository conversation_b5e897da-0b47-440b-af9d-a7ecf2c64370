
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import GlassCard from '@/components/ui/GlassCard';
import AuthForm from '@/components/auth/AuthForm';
import { Button } from '@/components/ui/button';
import { ArrowRight, Check, Shield, Clock, LayoutGrid } from 'lucide-react';

const Index = () => {
  const navigate = useNavigate();
  
  const features = [
    {
      icon: <Shield className="h-5 w-5" />,
      title: 'Secure Proctoring',
      description: 'Advanced monitoring to ensure assessment integrity'
    },
    {
      icon: <Clock className="h-5 w-5" />,
      title: 'Timed Sessions',
      description: 'Customizable time limits for each assessment'
    },
    {
      icon: <LayoutGrid className="h-5 w-5" />,
      title: 'Multiple Formats',
      description: 'Support for coding, SQL, multiple choice, and more'
    }
  ];
  
  return (
    <div className="min-h-screen">
      <Navbar />
      
      <main className="pt-32 pb-20">
        <section className="container mx-auto px-4 mb-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 animate-slide-in">
              <div className="inline-flex items-center rounded-full px-3 py-1 text-sm bg-primary/10 text-primary mb-2">
                <span className="flex h-2 w-2 rounded-full bg-primary mr-2"></span>
                Online Assessment Platform
              </div>
              
              <h1 className="h1">
                Secure and Intuitive Assessment Experience
              </h1>
              
              <p className="text-xl text-muted-foreground">
                Our platform provides a seamless environment for testing technical skills with secure proctoring and a variety of test formats.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  size="lg"
                  className="gap-2 btn-hover"
                  onClick={() => navigate('/login')}
                >
                  Start Assessment
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => {
                    const demoSection = document.getElementById('how-it-works');
                    demoSection?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  See How It Works
                </Button>
              </div>
            </div>
            
            <div className="animate-fade-in">
              <GlassCard>
                <AuthForm type="activate" />
              </GlassCard>
            </div>
          </div>
        </section>
        
        <section className="container mx-auto px-4 py-12" id="how-it-works">
          <div className="text-center mb-12 animate-slide-in">
            <h2 className="h2 mb-4">How It Works</h2>
            <p className="text-muted-foreground max-w-xl mx-auto">
              Our assessment process is designed to be straightforward while maintaining the highest level of security and fairness.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {[
              {
                number: '01',
                title: 'System Check',
                description: 'We verify your device, camera, and microphone to ensure a smooth experience.'
              },
              {
                number: '02',
                title: 'Demo Test',
                description: 'Try a short demo to get familiar with the interface before starting.'
              },
              {
                number: '03',
                title: 'Complete Assessment',
                description: 'Take your actual assessment with secure proctoring and monitoring.'
              }
            ].map((step, index) => (
              <GlassCard 
                key={index} 
                className="relative overflow-hidden group animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <span className="absolute -top-4 -left-4 text-8xl font-bold text-primary/10 group-hover:text-primary/15 transition-colors">
                  {step.number}
                </span>
                <div className="relative">
                  <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
                  <p className="text-muted-foreground">{step.description}</p>
                </div>
              </GlassCard>
            ))}
          </div>
        </section>
        
        <section className="container mx-auto px-4 py-12 mb-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 order-2 lg:order-1 animate-slide-in">
              <h2 className="h2 mb-6">Key Features</h2>
              
              <div className="space-y-5">
                {features.map((feature, index) => (
                  <div key={index} className="flex gap-4 items-start">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center text-primary shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1">{feature.title}</h3>
                      <p className="text-muted-foreground">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <Button 
                className="gap-2 mt-4 btn-hover"
                onClick={() => navigate('/login')}
              >
                Take an Assessment
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="order-1 lg:order-2 animate-fade-in">
              <GlassCard variant="dark" className="p-0 overflow-hidden">
                <div className="aspect-video bg-gradient-to-br from-primary/5 to-primary/20 p-6 flex items-center justify-center">
                  <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-lg w-full max-w-md">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex gap-1.5">
                        <div className="h-3 w-3 rounded-full bg-red-500"></div>
                        <div className="h-3 w-3 rounded-full bg-amber-500"></div>
                        <div className="h-3 w-3 rounded-full bg-green-500"></div>
                      </div>
                      <div className="text-xs text-muted-foreground">Coding Challenge</div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-5 w-3/4 bg-gray-200 rounded animate-pulse-soft"></div>
                      <div className="h-5 w-2/3 bg-gray-200 rounded animate-pulse-soft"></div>
                      <div className="h-5 w-full bg-gray-200 rounded animate-pulse-soft"></div>
                    </div>
                    <div className="h-32 bg-gray-100 mt-4 rounded p-3">
                      <div className="space-y-2">
                        <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
                        <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
                        <div className="h-4 w-2/3 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                    <div className="flex justify-end mt-3">
                      <div className="h-8 w-20 bg-primary rounded px-3"></div>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </div>
          </div>
        </section>
      </main>
      
      <footer className="bg-muted/30 border-t border-border py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-2 mb-6 md:mb-0">
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-semibold text-sm">A</span>
              </div>
              <span className="font-medium">Assessment Portal</span>
            </div>
            
            <div className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} Assessment Portal. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
