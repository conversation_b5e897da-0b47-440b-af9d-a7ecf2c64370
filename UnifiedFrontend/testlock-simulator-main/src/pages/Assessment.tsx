import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import TestInterface from '@/components/assessment/TestInterface';
import ProctorControls from '@/components/assessment/ProctorControls';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import AuthGuard from '@/components/auth/AuthGuard';

const AssessmentPage = () => {
  const navigate = useNavigate();
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [warningCount, setWarningCount] = useState(0);
  const [isExitDialogOpen, setIsExitDialogOpen] = useState(false);
  const isInitialMount = useRef(true);
  
  useEffect(() => {
    const requestFullScreen = async () => {
      try {
        if (document.documentElement.requestFullscreen) {
          await document.documentElement.requestFullscreen();
          setIsFullScreen(true);
        }
      } catch (error) {
        console.error('Failed to enter fullscreen:', error);
        toast.error('Please enable fullscreen for this assessment');
      }
    };
    
    if (document.fullscreenElement) {
      setIsFullScreen(true);
    } else {
      requestFullScreen();
    }
    
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
      if (!document.fullscreenElement && !isInitialMount.current) {
        setShowWarning(true);
        setWarningCount(prev => prev + 1);
      }
    };
    
    document.addEventListener('fullscreenchange', handleFullScreenChange);
    
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
      return '';
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    setTimeout(() => {
      isInitialMount.current = false;
    }, 500);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      if (document.fullscreenElement) {
        document.exitFullscreen().catch(err => console.error(err));
      }
    };
  }, []);
  
  const requestFullScreen = async () => {
    try {
      if (document.documentElement.requestFullscreen) {
        await document.documentElement.requestFullscreen();
        setIsFullScreen(true);
        setShowWarning(false);
      }
    } catch (error) {
      console.error('Failed to enter fullscreen:', error);
      toast.error('Please enable fullscreen for this assessment');
    }
  };
  
  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-b from-background to-muted/10">
        <Navbar isTestMode={true} />
        
        <main className="container mx-auto pt-24 pb-20 px-4">
          {warningCount >= 3 ? (
            <div className="flex flex-col items-center justify-center h-[70vh] animate-fade-in">
              <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center mb-6">
                <AlertTriangle className="h-10 w-10 text-red-500" />
              </div>
              <h2 className="h2 mb-4 text-center">Assessment Terminated</h2>
              <p className="text-center text-muted-foreground mb-8 max-w-md">
                Your assessment has been terminated due to multiple attempts to exit fullscreen mode. This is against the assessment rules.
              </p>
              <Button onClick={() => navigate('/')} variant="outline">
                Return to Home
              </Button>
            </div>
          ) : (
            <TestInterface />
          )}
        </main>
        
        <ProctorControls 
          isFullScreen={isFullScreen} 
          onRequestFullScreen={requestFullScreen} 
        />
        
        <Dialog open={showWarning} onOpenChange={setShowWarning}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-amber-600">
                <AlertTriangle className="h-5 w-5" />
                Warning
              </DialogTitle>
              <DialogDescription>
                You have exited fullscreen mode. This is against the assessment rules and may result in disqualification.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p className="text-sm font-medium">
                Warning {warningCount} of 3
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                If you exit fullscreen mode {3 - warningCount} more times, your assessment will be terminated.
              </p>
            </div>
            <DialogFooter>
              <Button onClick={requestFullScreen} className="w-full">
                Return to Fullscreen
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        <Dialog open={isExitDialogOpen} onOpenChange={setIsExitDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Exit</DialogTitle>
              <DialogDescription>
                Are you sure you want to leave the assessment? Your progress will be lost.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsExitDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={() => navigate('/')}
              >
                Exit Assessment
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AuthGuard>
  );
};

export default AssessmentPage;
