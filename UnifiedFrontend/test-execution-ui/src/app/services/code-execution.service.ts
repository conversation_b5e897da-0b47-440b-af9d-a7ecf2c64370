import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CodeExecutionService {
  private baseUrl = 'http://localhost:8080/testexecutionservice/api/code-execution';

  constructor(private http: HttpClient) {}

  executeCode(sourceCode: string, languageId: number): Observable<string> {
    const params = new URLSearchParams();
    params.append('sourceCode', sourceCode);
    params.append('languageId', languageId.toString());

    return this.http.post<string>(`${this.baseUrl}`, params.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
  }

  getResult(token: string): Observable<string> {
    console.log("Token is this  ",token);
    return this.http.get<string>(`${this.baseUrl}/${token}`);
  }
}
