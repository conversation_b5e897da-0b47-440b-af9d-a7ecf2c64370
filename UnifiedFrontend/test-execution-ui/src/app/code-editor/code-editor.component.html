<div class="coding-platform">
  <!-- Left Section: Problem Description -->
  <div class="problem-section">
    <h2>Problem Statement</h2>
    <p>{{ problemDescription }}</p>
  </div>

  <!-- Right Section: Code Editor & Output -->
  <div class="editor-container">
    <!-- Header -->
    <div class="editor-header">
      <div class="editor-header-left">
        <label>Select Language:</label>
        <select [(ngModel)]="selectedLanguage" (change)="changeLanguage()">
          <option *ngFor="let lang of languages" [value]="lang.mode">
            {{ lang.name }}
          </option>
        </select>
      </div>

      <div class="editor-header-right">
        <button (click)="adjustFontSize(-1)">A-</button>
        <span class="font-size-label">{{ fontSize }}px</span>
        <button (click)="adjustFontSize(1)">A+</button>

        <button (click)="toggleTheme()">
          {{ isDarkTheme ? '🌙 Dark' : '☀ Light' }}
        </button>

        <button class="run-btn" (click)="executeCode()">▶ Run</button>
        <button class="submit-btn" (click)="submitCode()">✔ Submit</button>
        <button class="fullscreen-btn" (click)="toggleFullscreen()">⛶ Fullscreen</button>
      </div>
    </div>

    <!-- Code Editor -->
    <div class="app-ace-editor" #editor></div>

    <!-- Output Section -->
    <div class="output-section">
      <h3>Output:</h3>
      <pre>{{ output }}</pre>
    </div>
  </div>
</div>
