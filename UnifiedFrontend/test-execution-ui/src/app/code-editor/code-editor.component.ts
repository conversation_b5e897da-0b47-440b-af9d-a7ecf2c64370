import { AfterViewInit, Component, ElementRef, ViewChild } from "@angular/core";
import * as ace from "ace-builds";
import { CodeExecutionService } from "../services/code-execution.service";

@Component({
  selector: "app-code-editor",
  templateUrl: "./code-editor.component.html",
  styleUrls: ["./code-editor.component.css"],
})
export class CodeEditorComponent implements AfterViewInit {
  @ViewChild("editor") private editor: ElementRef<HTMLElement> | undefined;
  aceEditor: ace.Ace.Editor | undefined;
  
  fontSize: number = 14;
  isDarkTheme: boolean = true;
  output: string = "Output will be shown here...";
  
  // Problem Description
  problemDescription: string = `
  Given an integer array nums, return the sum of all elements.

  Example:
  Input: nums = [1,2,3,4]
  Output: 10
  `;

  // Default Code
  code: string = `public class Main {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
  }`;

  // Supported Languages
  selectedLanguage: string = "java"; // Default
  languages = [
    { name: "Java", mode: "java", id: 62 },
    { name: "Python", mode: "python", id: 71 },
    { name: "C++", mode: "c_cpp", id: 52 },
    { name: "JavaScript", mode: "javascript", id: 63 },
  ];

  constructor(private codeExecutionService: CodeExecutionService) {}

  ngAfterViewInit(): void {
    ace.config.set("basePath", "https://unpkg.com/ace-builds@1.4.12/src-noconflict");
    this.aceEditor = ace.edit(this.editor?.nativeElement);
    this.aceEditor.session.setValue(this.code);
    this.aceEditor.setTheme("ace/theme/twilight");
    this.aceEditor.session.setMode("ace/mode/java");
    this.aceEditor.setFontSize(this.fontSize);

    this.aceEditor.on("change", () => {
      this.code = this.aceEditor!.getValue();
    });
  }

  // Change Language
  changeLanguage() {
    this.aceEditor?.session.setMode(`ace/mode/${this.selectedLanguage}`);
  }

  // Adjust Font Size
  adjustFontSize(change: number) {
    this.fontSize += change;
    this.aceEditor?.setFontSize(this.fontSize);
  }

  // Toggle Fullscreen
  toggleFullscreen() {
    this.editor?.nativeElement.requestFullscreen();
  }

  // Toggle Theme
  toggleTheme() {
    this.isDarkTheme = !this.isDarkTheme;
    document.body.classList.toggle("dark-theme", this.isDarkTheme);
  }

  // **Execute Code**
  executeCode() {
    this.output = "Executing...";
    const sourceCode = this.aceEditor?.getValue() || "";
    const languageId = this.languages.find(lang => lang.mode === this.selectedLanguage)?.id || 62;

    this.codeExecutionService.executeCode(sourceCode, languageId).subscribe(
      (token) => {
        setTimeout(() => {
          this.getExecutionResult(token);
        }, 3000); // Delay to allow execution
      },
      (error) => {
        this.output = "Execution Error!";
      }
    );
  }

  // **Get Execution Result**
  getExecutionResult(token: any) {
    this.codeExecutionService.getResult(token['token']).subscribe(
      (result:any) => {
        this.output = result['stdout'];
      },
      (error) => {
        this.output = "Error fetching result!";
      }
    );
  }

  // **Submit Code**
  submitCode() {
    this.executeCode();
  }
}
