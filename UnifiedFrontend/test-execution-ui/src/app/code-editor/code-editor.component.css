/* Frame & Layout */
.coding-platform {
    display: flex;
    flex-direction: row;
    height: 90vh;
    gap: 10px;
    background: #1e1e2e;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
    border: 2px solid #343a40;
  }
  
  /* Problem Section (Left) */
  .problem-section {
    width: 40%;
    padding: 20px;
    border-right: 2px solid #ddd;
    background: #f8f9fa;
    overflow-y: auto;
    border-radius: 10px;
  }
  
  /* Editor & Output Container (Right) */
  .editor-container {
    width: 60%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
  }
  
  /* Editor Header */
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #2a2d36;
    color: white;
    border-radius: 8px 8px 0 0;
  }
  
  /* Language Selector */
  .editor-header select {
    padding: 6px;
    border-radius: 4px;
    background: white;
    font-size: 14px;
  }
  
  /* <PERSON><PERSON>yling */
  .editor-header-right button {
    padding: 8px 12px;
    border: none;
    background: #007bff;
    color: white;
    cursor: pointer;
    border-radius: 6px;
    font-size: 14px;
    margin-left: 8px;
    transition: all 0.2s ease-in-out;
  }
  
  .editor-header-right button:hover {
    background: #0056b3;
  }
  
  /* Font Adjustment Buttons */
  .font-btn {
    background: #ffc107 !important;
    color: #212529 !important;
  }
  
  .font-btn:hover {
    background: #e0a800 !important;
  }
  
  /* Run Button */
  .run-btn {
    background: #28a745 !important;
  }
  
  .run-btn:hover {
    background: #218838 !important;
  }
  
  /* Submit Button */
  .submit-btn {
    background: #17a2b8 !important;
  }
  
  .submit-btn:hover {
    background: #138496 !important;
  }
  
  /* Fullscreen Button */
  .fullscreen-btn {
    background: #6c757d !important;
  }
  
  .fullscreen-btn:hover {
    background: #5a6268 !important;
  }
  
  /* Font Size Label */
  .font-size-label {
    font-size: 14px;
    padding: 0 8px;
  }
  
  /* Ace Editor */
  .app-ace-editor {
    width: 100%;
    height: 60vh;
    border: 2px solid #ccc;
    border-top: none;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.2);
    border-radius: 0 0 8px 8px;
  }
  
  /* Output Section */
  .output-section {
    flex-basis: 40%;
    background: #1e1e1e;
    color: #f5f5f5;
    padding: 15px;
    font-family: monospace;
    overflow-y: auto;
    border-radius: 10px;
    border-top: 2px solid #444;
  }
  